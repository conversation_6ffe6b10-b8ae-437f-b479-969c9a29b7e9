{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cf2c958e3fd5844d27f0fefedd75e7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986bfe3a1d57cbd548aa91fe3e8be18ee7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891780d8e50aa81bd3d0f6b9bdb9bef61", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9871a0238ab43ea88422d06780d1417580", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891780d8e50aa81bd3d0f6b9bdb9bef61", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9866615d5b5b834b53f582d7bfd31e825f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9898718c5fa4ea45a9f89b23c586437878", "guid": "bfdfe7dc352907fc980b868725387e98d45b4c4a4db45f174ced5cc8951b2483", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a31440591d9ab67598139c9014ec195", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f00ceaa487bcfc23de96d1f0b2d3dc85", "guid": "bfdfe7dc352907fc980b868725387e9880ffc0fb88e6f243693e7dc87aad14e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a32d1e4b76be7c702977712d3242c6", "guid": "bfdfe7dc352907fc980b868725387e9875280331116d0db50f5d5fd46a3ff8a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e3f0143fa47f0ab35e4eaeb06f9875b", "guid": "bfdfe7dc352907fc980b868725387e98d3e4e5b965e8271e5b89bfc87ba8be80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b590c620895235cbb57b49122a9ce752", "guid": "bfdfe7dc352907fc980b868725387e98ba5e3d7483df391ddc97fcb5d9d01932"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326bf48cb00cb735179531e1856322ea", "guid": "bfdfe7dc352907fc980b868725387e989a1c96f80219be972057f42679bc07b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdf30adee1a26046b7dc404c5e282d0c", "guid": "bfdfe7dc352907fc980b868725387e9862630578f724628584db6a9819f19355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13df8cc0db0cf4764bb6c6c8d3d1aa6", "guid": "bfdfe7dc352907fc980b868725387e983b3ab7f0d2d8f45f0f5e24b3fe4eb0b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980641fd9425bf354a8e28826653be384d", "guid": "bfdfe7dc352907fc980b868725387e987d24f30e053764ce0fa35ba670376b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafed8317f28b19cb24d4f27f27c51b0", "guid": "bfdfe7dc352907fc980b868725387e98b9615984c36dc61160c9a2d7a9af715a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc282cd5d75ce7eb6f13280c286c6d10", "guid": "bfdfe7dc352907fc980b868725387e9884fb75c1545ef2b45b406f48a4a46519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8f9f413f3844bfce1fe635fa0a6dd7a", "guid": "bfdfe7dc352907fc980b868725387e9891c49d3783f565e009976fb592d92fef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee7793c3007f0392981669a3ec97e9f", "guid": "bfdfe7dc352907fc980b868725387e986de5bae94d2b73cc9fe9ca65ac227c15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bb227a86abad010673baa5ac0d7d93b", "guid": "bfdfe7dc352907fc980b868725387e984ea73c31b5a07035cc3bd48745a466b4"}], "guid": "bfdfe7dc352907fc980b868725387e987aea623a5c5ef84f2944e992d19f4f0a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e984ed4b3d490bda62194e9205a9aa1f335"}], "guid": "bfdfe7dc352907fc980b868725387e98e7468791fe68423df85ccd395eda7ab8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e536b50698bde7f1e789c702f7bd5ac1", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e980ef436bbf5ebb83886c8fecfb3ef415f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}