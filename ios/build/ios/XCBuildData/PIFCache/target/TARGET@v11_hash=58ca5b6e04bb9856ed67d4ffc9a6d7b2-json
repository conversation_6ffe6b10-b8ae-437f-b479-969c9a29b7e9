{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9802f9092b1d9cce537842afcc21acaad0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980b59009e93ff95f9081504d3620423f5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bc1dee6d5244600e681131af5f50cbc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bb8365b56bceb2a03ef67ba03f9e1560", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982bc1dee6d5244600e681131af5f50cbc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c624b75abe1d066a1ca784387dfd41f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983d3748c5eb49d5c74d28c9e44abb2fb2", "guid": "bfdfe7dc352907fc980b868725387e9803053f608259ce73e0d33a6436b32d7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d4f42d6d9edc4b5198e685cf00cb6ac", "guid": "bfdfe7dc352907fc980b868725387e989608b53e2e3190c898881bb372c6c83b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5f5d0e9c2da2a2a432e463bba964a6", "guid": "bfdfe7dc352907fc980b868725387e98de65d2d87e1f510965793a6275105aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c79c571f0dbeb0467a32853110963da", "guid": "bfdfe7dc352907fc980b868725387e98b325eb9e54f5c7aee2b5252b82eb22ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af5e20f7b953f9c3c191a6322f6d0ed1", "guid": "bfdfe7dc352907fc980b868725387e98421c8915ea14a0d00f298fc34217b5c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba12e1356e4ae272db282d4845a5f75", "guid": "bfdfe7dc352907fc980b868725387e98289471136e8e1c4f37fc426fbc33f341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e327268f34492a08974e9c3ffb15672", "guid": "bfdfe7dc352907fc980b868725387e98fadd36f2be2c491dca43d4207bfa4db4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae8d2a4dad7012fb186c7ae625261a4", "guid": "bfdfe7dc352907fc980b868725387e98a48e423d75ca93d7aa5a03c18d240ef1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801a99f19c502d2478a4286edceeab71e", "guid": "bfdfe7dc352907fc980b868725387e987bcc27b41c5f09acf0707bbdae470879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984009b248ddb4cd78f484657b275c3a09", "guid": "bfdfe7dc352907fc980b868725387e982f8c400786af2e1795c190a7540d149e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba0d1c1c7b0effabde627e080b92d410", "guid": "bfdfe7dc352907fc980b868725387e988576ddbbefd1719050b09db0f3122a38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e7abaaa4f002e87f4d128cf09e2a470", "guid": "bfdfe7dc352907fc980b868725387e98757fe5a89117a258f074e7932885e1af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aab20805a1bc2af2cc2803100cfc99c3", "guid": "bfdfe7dc352907fc980b868725387e983755394d456b6fc5f184583a9fe49e8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ed8fdc7f48365fd026d028a0010053", "guid": "bfdfe7dc352907fc980b868725387e98502a5b57280b4d5a60b75155258ffb43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f91627efeffbd1368b826e509128ad", "guid": "bfdfe7dc352907fc980b868725387e98d273673d2bdeae5bd9dc24ea5d1913bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836390692b3737e0a298ff071d3eaa250", "guid": "bfdfe7dc352907fc980b868725387e98f6be2736cc0a3f868a700518d797f90f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98899ee68daed36c43cf833718b517fe8c", "guid": "bfdfe7dc352907fc980b868725387e988469a3d5ab6dc330d84e672858009641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980990b8ed4bdfab5e577932124703d912", "guid": "bfdfe7dc352907fc980b868725387e980b30c11fb2446facf01cd78f13347838"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986778f8602627d4a1a9a02a081b3f14dd", "guid": "bfdfe7dc352907fc980b868725387e98272a5904788e7dcdd646f23325324833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ed1ccf8061437435b97e5b921c7ad9", "guid": "bfdfe7dc352907fc980b868725387e98d0742c81cdf99b339f7578f6c8c4f1ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b442061d6a8e234862ff8f96c99c0c94", "guid": "bfdfe7dc352907fc980b868725387e98358c1f3b58508b96ce49937a036a7211"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55460eb36efb1f30bffe4a039444ea9", "guid": "bfdfe7dc352907fc980b868725387e98bbc9b86364c3469804f38d249dc2fc57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7ffc452945be002470c163e45ad398a", "guid": "bfdfe7dc352907fc980b868725387e9878381c6b60cccdd0d4cd3974804c0c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818075ce9288a411bed73d62116a963bf", "guid": "bfdfe7dc352907fc980b868725387e984ff8023bb15681f85a531f274113ba21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98630e570d49a997c66c1c0caadf943955", "guid": "bfdfe7dc352907fc980b868725387e983e0b4c457c1c7a104822cfe7205b87ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98323844b8268ef92fe9b36b3ad24b65df", "guid": "bfdfe7dc352907fc980b868725387e98e2626fd1c8010ed3fc91c9a41b20622e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835b13ef42770bdea1802da9b81d063c2", "guid": "bfdfe7dc352907fc980b868725387e98e9ade578335e5365524e26c4fa71b655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bedc2d26d55eb0f6a44d66bfb529a7c1", "guid": "bfdfe7dc352907fc980b868725387e98663c3b9d27fd6cb82c09ef23895bd761"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811897be9fe8277730d594a82ae543961", "guid": "bfdfe7dc352907fc980b868725387e982460abfa569630bc27e3be0e21c3a0a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c86554d123768e6f31bf3f67e5fa2f7", "guid": "bfdfe7dc352907fc980b868725387e989822dab12e7d3baf1e0b7bf905426bc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b41214cb3a98349e0acede4dc9350b0", "guid": "bfdfe7dc352907fc980b868725387e981234901e72195b43ac671acb803cd721"}], "guid": "bfdfe7dc352907fc980b868725387e9812685512b1405ca4d74bf99ce8fb89af", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9873400ec73a63b32c9b6d9b2a496f0682", "guid": "bfdfe7dc352907fc980b868725387e98064a87490d233caa35fe6abf523cc941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f37780d2f5e395e430091807a07822", "guid": "bfdfe7dc352907fc980b868725387e98e0cbc0e44f0705f831fa2abe0b59426d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891021c65b294e73219738ce2e798e7a3", "guid": "bfdfe7dc352907fc980b868725387e987eb53b36c9d5504b37c98fa3ef5c4cca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1378ac0c2edcd214a55025f2768d33", "guid": "bfdfe7dc352907fc980b868725387e98d7fdc500b02fe58166c8371dffcb88ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d66b4b9a5bc946c16308ab018ed3bd", "guid": "bfdfe7dc352907fc980b868725387e980d5a4444e7ce3624be479e0e66f4946f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c92ce946d0af763fbc0458099f9ccf3a", "guid": "bfdfe7dc352907fc980b868725387e984315eb9358d1fb3d71be25ddaabaf2c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d10a782da188f216e2c7ad1457a1914", "guid": "bfdfe7dc352907fc980b868725387e9897b451da04d48e3f522eea5fb4f0b30e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822302f79aa61c52616fab2437bbc70b6", "guid": "bfdfe7dc352907fc980b868725387e98dbf6145aff431c7a81c89b4d9bb390af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0d7c8c825912c372d2f1f964ffb0dad", "guid": "bfdfe7dc352907fc980b868725387e9803c437d2f84559caee4a4c8b0b3ad481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be0e37e800fc4ab847099ee80f710f5", "guid": "bfdfe7dc352907fc980b868725387e98e4387d2d4de2e3d8dc287ca6aafd5bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873a1d48e522e50b855ce37840d71fd4", "guid": "bfdfe7dc352907fc980b868725387e98d814791957d0d153dc6d570ce9e5f9ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8bbc75716d009bf0966777a647990b", "guid": "bfdfe7dc352907fc980b868725387e986f50398c1270f70cd8d51ac696820fa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f67067af580f6ec48111f6af3120ba0c", "guid": "bfdfe7dc352907fc980b868725387e98a4537735b7cfde92b68b41cbb6769307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5d7093e748a4c97cf406b99a155693", "guid": "bfdfe7dc352907fc980b868725387e98d54d442929bdd916f276790930061488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d546885f8eec723c77f2daf6bc1fc27", "guid": "bfdfe7dc352907fc980b868725387e98050b6245e4e923e4f17c9e195e473f49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c979a6448db9e4084a0eb4194d449006", "guid": "bfdfe7dc352907fc980b868725387e98f9104839562a4c9e0b58fb40ca9edafb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98884134bb8dce36303a0985e98da458d7", "guid": "bfdfe7dc352907fc980b868725387e98b788ef930f723f63948b728da7ec7545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889887597aea6f65d9699413159fa285a", "guid": "bfdfe7dc352907fc980b868725387e987e87d4996c5ce53e5e33e1d27a5b7086"}], "guid": "bfdfe7dc352907fc980b868725387e98ab8bf69247507582ea1c4bd8415d0600", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98891046fd753bf74ec33f5694cd7819e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e98c66f29d0b4c36df7e4d67114d28167be"}], "guid": "bfdfe7dc352907fc980b868725387e984eb8ff2ed5cbb931d4e43c95ef45bfe6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985e053717185bf5083459d86406594e73", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9881269fc29ba60af815ad38fdc068704f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}