{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98854ec9cc2c58c1ff1a6e468d662e2ab8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987001da03c042b84a105f6be9058f42ed", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98690e37bd573e2a8b42095574125c1dc4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5ea1fe5688f2258ad947eaafe2d7b95", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98690e37bd573e2a8b42095574125c1dc4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db9b851a0d0c703b99d0004d999acfd1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98470ceb9cee6a48a1160278614b2a02ef", "guid": "bfdfe7dc352907fc980b868725387e988b490bbac336b7e7e273e077fad2f5a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c185d70a98e6439e5ca782c662e093", "guid": "bfdfe7dc352907fc980b868725387e982b3af250e8eb3ffb5fcac2326b4142d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b3ccf9cbb66ad424ff8d77860c2c88", "guid": "bfdfe7dc352907fc980b868725387e986ad8e04c4fe59515e11af78623d94f4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede5158b90de702dba5106c4e1823933", "guid": "bfdfe7dc352907fc980b868725387e98619746e80eaeab4c879961caf836a128", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dec96d638c2e6085abcbdb65c8df45c5", "guid": "bfdfe7dc352907fc980b868725387e98768996e0ff9b0d72a88e63bc2a12d5e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a76554bbdf318aae71390b472818e2", "guid": "bfdfe7dc352907fc980b868725387e9880026d1f96dd069154e24b5560f5458a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac013b4b8a34dce9c9a109420fb1f4a6", "guid": "bfdfe7dc352907fc980b868725387e9877438324df8df4fec0f20a2062f35012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d96b917e1f8cc41cc27302b1a441ed0", "guid": "bfdfe7dc352907fc980b868725387e988b6f76b3192cab54f1a74ebfc53e5114", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983204db217795a8ab59a27e7944b1037a", "guid": "bfdfe7dc352907fc980b868725387e9841faf8710cab771b447fc7f3dc3cf3cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c0a8e442d5613fb8969136f866d260", "guid": "bfdfe7dc352907fc980b868725387e98fe4c6f9b36738f4058ee5046fcc7759d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f953469bc0d36de0fb4836af30603a45", "guid": "bfdfe7dc352907fc980b868725387e98583549eb59437195498fc8f5afcb25ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892ed72e58df462213d565b55ca16281", "guid": "bfdfe7dc352907fc980b868725387e985a499fbe57e18085291bc81b8a87a928", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854805324bd1abaaa781290206af835fb", "guid": "bfdfe7dc352907fc980b868725387e98486e96bfc81e1892895854a224f340dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba763e2c17561b8fafacc2de4ca20eff", "guid": "bfdfe7dc352907fc980b868725387e98a6ca38bf232592e18d7740d3d13e4a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5cd1568f0f815ec314af43a2b6eefd2", "guid": "bfdfe7dc352907fc980b868725387e984d71be1be6748a02dfcdc9dfd30afab4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a310ef24e3f7ac2e0579fc7a8335e3", "guid": "bfdfe7dc352907fc980b868725387e98097a7bd4574ad3a4d553297d15933eb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98748ee535344e1a33233bfdc6e1d55cc5", "guid": "bfdfe7dc352907fc980b868725387e9821c7e8d367d26c9bcd36e54b772f7171", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b32f37d89ef184742f52dd8da097f48", "guid": "bfdfe7dc352907fc980b868725387e988a5dcd04d0670293e4359a521f8b3d68", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53c4533e34da74c49dfe7dccc4da9d7", "guid": "bfdfe7dc352907fc980b868725387e987115c2a6f01e9b5b496a074babc7187c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ce02a9bd7a1ed3e7c0b0f42ce44f1b", "guid": "bfdfe7dc352907fc980b868725387e98d2a17ea50739cf212d401afb0eb707fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983258b3d10d7c16c19f8e1ee55f8392d9", "guid": "bfdfe7dc352907fc980b868725387e98fd7a3caa39113eb58ee782632343e3fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b245b521dfcbef8e61f72d1ee5a7e3", "guid": "bfdfe7dc352907fc980b868725387e988d26a4e2767992d141cfedded6fcc671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b24e966009b8c71ff36e2d2d1daffef1", "guid": "bfdfe7dc352907fc980b868725387e9889f92b35fac8f7788f4f8b3026f39e36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a271b83b9739b1201e07accf4bfa1826", "guid": "bfdfe7dc352907fc980b868725387e98e51a6118f7163d7045be216e615c8dbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803af980bf008f6eb89d7ae586a8b7792", "guid": "bfdfe7dc352907fc980b868725387e988560262564be7260c7c315f9b82bbabf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ba2a6c324d5b19dc48820944fe27145", "guid": "bfdfe7dc352907fc980b868725387e98f7246af090cc2c1e9dee62546ad939b4"}], "guid": "bfdfe7dc352907fc980b868725387e98e9bbdb3127b654867db9a05e156af24c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9885b3a4d81531a84c3b0b6ff775c1fb99", "guid": "bfdfe7dc352907fc980b868725387e98cb05cc2b99473fcb18f9d4b433d40347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8f17b4443aea834874cde7cb0d13886", "guid": "bfdfe7dc352907fc980b868725387e987223bfa4a4af961eb6fcaf1f5e55db1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b57a133f0e428c6a8994f22993587e01", "guid": "bfdfe7dc352907fc980b868725387e985ab02f8707dbd7d29f3fd0d4b1130bfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ea7f3503dbc5d1718435bc54bbeb10", "guid": "bfdfe7dc352907fc980b868725387e98d453785b0e730750db82cb11d74e4520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fe261ec16755fc880722144e43d7f6d", "guid": "bfdfe7dc352907fc980b868725387e98f1430d2924fb372816789f5a3787ac54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05555ed7c29d3099e123a906d15beee", "guid": "bfdfe7dc352907fc980b868725387e98e1879f8a30c5f61f70703511a81acbac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bd9b1713118a06592b4ad1cdd8ed9de", "guid": "bfdfe7dc352907fc980b868725387e98007d5abf7dbb91f51a423de3187ca339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792a65f408a80e22145be874063d3c38", "guid": "bfdfe7dc352907fc980b868725387e9813d889a327c5e9ae39abe348c43c8ecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e634cf51a2ed154d241c8a611741f693", "guid": "bfdfe7dc352907fc980b868725387e9826326e3ba800c4354f20fac50f6fe34e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98046661f741c647f99d7aff549b64a19e", "guid": "bfdfe7dc352907fc980b868725387e9896661e0c65f8431a9acddade468b8fc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98993bd934b664581cc6e25ba9f8f97ca3", "guid": "bfdfe7dc352907fc980b868725387e98233a6cdbb1359a43f9e4af1b6e4b9e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813679f633c54888c2e90d99814a9144b", "guid": "bfdfe7dc352907fc980b868725387e980427cc8f0308b790f59cfa65df734d3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d1ff0f8075f70aed60a4b80b511dd05", "guid": "bfdfe7dc352907fc980b868725387e9844a6dfbd7784aac1c702850711bfa755"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811cbfe616fd5cc76ace1a59b74ac40cb", "guid": "bfdfe7dc352907fc980b868725387e9859ff13297e0f646901bbb5051e983874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e5965f7cf6b6fcd7e439c26bfadf97", "guid": "bfdfe7dc352907fc980b868725387e9867efb0ab25c9724becd77c1a81c37f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838c0a929e68398881f8e57a982b8b8ab", "guid": "bfdfe7dc352907fc980b868725387e983e37044f84d95389b0dd95489f77f000"}], "guid": "bfdfe7dc352907fc980b868725387e987ec92050abd0a0db0c2025a301665248", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980cb496842a810c122fb97ed3a7b3d5e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e980c883300957792bcb2f01774b557e9c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e9897ae9780b61bffa94573d51f641d6164"}], "guid": "bfdfe7dc352907fc980b868725387e98b3d5991361a4d34f63718b6559a9bbaf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e24502461729ad58db50ec72751d250c", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98a3afd6240ef7ba57544d14d6ec3899c7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}