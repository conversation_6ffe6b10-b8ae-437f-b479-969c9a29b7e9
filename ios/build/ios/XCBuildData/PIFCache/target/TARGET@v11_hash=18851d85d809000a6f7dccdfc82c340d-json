{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c6b77eaf6e189c5b31dabf3293b4cca7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dd35a743a23dd9138f32871933fe2c45", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2e005f7f6e5e46320aa29ce229060e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f9bc9151f3d0c6c5351cb53088b62e7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f2e005f7f6e5e46320aa29ce229060e0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827189268b93a9dc6aeaf6cc0d8cd857a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981927ef64b62c1c53e6f0bf4c74c3f04c", "guid": "bfdfe7dc352907fc980b868725387e98dc7769dd7124687e13fbba15de1ef40f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29e16725201f8eb930cfca81a677276", "guid": "bfdfe7dc352907fc980b868725387e9810d8158b50f3cc69776bc163f0647f18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b25441630c470127c8f96326bd9edbc0", "guid": "bfdfe7dc352907fc980b868725387e98283f5f7acdb362ff071a28a50563f910"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28a692ded1df1e7b66b1105c2a46022", "guid": "bfdfe7dc352907fc980b868725387e98914fa597e9c76c80e74e03f4c0bfe95e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e7b67b135de2c38dbe891b935209dc", "guid": "bfdfe7dc352907fc980b868725387e986365cabd36c67255b48dff93e9b92699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d95948fc0eb0c8b4a89ff22c96861e2e", "guid": "bfdfe7dc352907fc980b868725387e9814f8fc1c694d3ed0ca947f924d436141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dcb22ad3e7bf968ff7f37b3bff2e713", "guid": "bfdfe7dc352907fc980b868725387e98f0ca7107066707f747e938667a165e3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345356a07a6d674db44c63f74541c533", "guid": "bfdfe7dc352907fc980b868725387e98424b4169f07b56c19cbc1392d4b5ea88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bf39871f68d7d53cb29abafe6632ce6", "guid": "bfdfe7dc352907fc980b868725387e9889658311f97a8695916f69ac63a50974", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6aafd79341147478914cafa9d293774", "guid": "bfdfe7dc352907fc980b868725387e981453ffb5afd3768205683b1991640782"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d917238a8db6853a5424e185dd477a", "guid": "bfdfe7dc352907fc980b868725387e98377415f2526be0697147d69896b49e32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870d5de6602525ad66c23afe56873e269", "guid": "bfdfe7dc352907fc980b868725387e98412460d370ef421973ae1d2ac1fc6fda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7fa479637f002af8fad6ca00d1c84e", "guid": "bfdfe7dc352907fc980b868725387e98118da89045ec8cc10deed329d02c6e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c47ce5824b6c05d0e34d10e6db8f8298", "guid": "bfdfe7dc352907fc980b868725387e983e3435c308fa7c167567cf397b0cc2fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c047f9f764833a3808bba520e4769476", "guid": "bfdfe7dc352907fc980b868725387e9899fc5a75ac40e02333fea0f5b65e801c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986184b0c1b8e6aafc662fa12d84584066", "guid": "bfdfe7dc352907fc980b868725387e9859c2723b8dca74822d49eaafa65434aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ad4af9d6fef8741ee34ea2fba6325fc", "guid": "bfdfe7dc352907fc980b868725387e9870fec546d85375c4fda19467c0bbf337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c227ee42849585dfa7e3eb71b716f4e7", "guid": "bfdfe7dc352907fc980b868725387e98b6ddf881eb12ee2fb244983dce372cc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d27f0777cf847bd1f8dc8ad8546def61", "guid": "bfdfe7dc352907fc980b868725387e98462ecaaeb471c9cc30b93b4123b934a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371b66f7b73c6f90b423a8d5b7ac275c", "guid": "bfdfe7dc352907fc980b868725387e9813d8a4899946bf58c7b7b343850ba35a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983f699a9898855f1e13356f0438436c", "guid": "bfdfe7dc352907fc980b868725387e9890b65e2d27345e1d0a95b5b08c514e94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a046819ba7a8b2640398407684670020", "guid": "bfdfe7dc352907fc980b868725387e985d85af3ff8770ca7bd0b183ec36a6c74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d369764a14ac3f9438865a067a81847", "guid": "bfdfe7dc352907fc980b868725387e98f0522dc18af1b79d8bb31466153c96cb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982a01942a196d3b89019389122419b3b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b01367f182c4cf48936555693606cd9", "guid": "bfdfe7dc352907fc980b868725387e9893d77edf5ce231ffaa3b99b9817ddd87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f31875f13bbc90821b5f252d93a83d51", "guid": "bfdfe7dc352907fc980b868725387e98d172121e3bc6cb9a6670a76f6a5ae190"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893b37daafeb25efc39101cc97acfa5f7", "guid": "bfdfe7dc352907fc980b868725387e9819c95a690032f1171aae301c83399ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c9aa57228fe7572627e9b0c4c2481cf", "guid": "bfdfe7dc352907fc980b868725387e98ff0b4a73c2c537311877e7dcb1e23c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ac1705cbc7313605708c93a575e618", "guid": "bfdfe7dc352907fc980b868725387e98290292fa6175df6d7470016a98a848f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53b2003f264150bd88092d03c599a3d", "guid": "bfdfe7dc352907fc980b868725387e986dab1e9972842b1de5b70d1e0aac38ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d48c4c558275d4d78809e7c56d94f6", "guid": "bfdfe7dc352907fc980b868725387e9874ac1563b39e12d1de60231dbf35bc2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e908bff37f30de7241e5b0d23afb5b02", "guid": "bfdfe7dc352907fc980b868725387e98c3c4c2894010a9a0c571bcd222487a32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f31747ef60e5b267e207f7508009fc1", "guid": "bfdfe7dc352907fc980b868725387e981b98c5eea72474a5520bb562f99e50f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d62d8767c5d16fca71cbaf881e58b9", "guid": "bfdfe7dc352907fc980b868725387e98ff6862af5c20db82949473797dc57735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842146b32e1e536d4fca59d3ca32780a5", "guid": "bfdfe7dc352907fc980b868725387e9810b44fb99ad93280ae3e1c2c8dfa1ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98035caa047356b65230941bb80c405dcd", "guid": "bfdfe7dc352907fc980b868725387e98b2ea9da54d6a75829cb603d73648d7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0f40de9549423cf6d52c4007297220", "guid": "bfdfe7dc352907fc980b868725387e987961cf06f4f91be2e462653c37a87be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986330c4cfcaadffdf5e88c630a006865a", "guid": "bfdfe7dc352907fc980b868725387e98923297e90292a00926da59eaa103e0bb"}], "guid": "bfdfe7dc352907fc980b868725387e9801ba4195ffe61f449d24369c7c48b677", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98f9ec6d0f0e1faee8e403a4e938457de3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e98e9f2a9d04098ddc2cbed80a53fb140e9"}], "guid": "bfdfe7dc352907fc980b868725387e98562c5924935f85b6b203707bafc075a9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d5ac900c35eb7a095d8c1c6a308b34ed", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e988c97f36a500faf63ead25160ce384cd8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}