{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7a0791b206e2ee5c1aa151fdf7e3030", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c1a49d2eb4928f38153fbb752b43f0d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987629a1c96831e47571ebe8eb88789eb9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a061109697a6fecb5c4052027830873", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987629a1c96831e47571ebe8eb88789eb9", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a2bfc0afebe00a97dd280fbd3b086c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9855856216766863fb14d9352ac37c7434", "guid": "bfdfe7dc352907fc980b868725387e98305321bb0f6c61d76ab6f8f834f9e1bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989c7318c71e29c5c2fa728fab937f84e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859a6e0c18635baab2852b981dad7eb1c", "guid": "bfdfe7dc352907fc980b868725387e986b9c07eb90d1c19fb7ac9f978eac2cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895e6d890ba31545745751e9697143de7", "guid": "bfdfe7dc352907fc980b868725387e9899bf2eef86ec86d3071d0464ba311bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a267f4ea3a5bba0d23b245b35dc8a0e", "guid": "bfdfe7dc352907fc980b868725387e989923225760583f9044f3477d68370fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dc08d0c4df8d05c0f790c3aa7b17d09", "guid": "bfdfe7dc352907fc980b868725387e980b8fe6bd9ce7b7f7f9b672f675ac40ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5a8fb6ac995ba14132018cb65d5fb5c", "guid": "bfdfe7dc352907fc980b868725387e98c7914d6db7a604491a406b7ffd04ffdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812485a73b7a9c04a8cc30bca6caf6eda", "guid": "bfdfe7dc352907fc980b868725387e98eb2913c855fd2bba90610341d1005093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c0526a80c98ea61d095edeabebc69c", "guid": "bfdfe7dc352907fc980b868725387e9818bf3e3f4e2e4ff6409fd3de93a3e67b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bac5e3f211b976afc523ef58e0747a1", "guid": "bfdfe7dc352907fc980b868725387e9862f3f56aa2a92efeb6d501703a17fb78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a68f82c4a870841fe98ecd6ffd9e927", "guid": "bfdfe7dc352907fc980b868725387e986f0a5a4d4d049699b38b834e5127e27c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987290b61104e5f125f018e1d5e5336b6f", "guid": "bfdfe7dc352907fc980b868725387e98b11d52cb23817759d43df3901c9dfdcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3aa835e1c6edaf72585920e6b14549f", "guid": "bfdfe7dc352907fc980b868725387e982e2cfaddbe60c31e36e71f4cf7b78cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee1bf170557b876a658ce8999d2392c", "guid": "bfdfe7dc352907fc980b868725387e9880b17e68f279e7d7783a51e9bb5287b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44c33956f57201c404fb57e4378b168", "guid": "bfdfe7dc352907fc980b868725387e98edb05713e1e170990070d795fbfd9ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627ef69cdcef630cb28ee0e8d74fc8e9", "guid": "bfdfe7dc352907fc980b868725387e98571373774a6c562d2a57eeef6625aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9054920b2b56dffc93c67a1a7f6985", "guid": "bfdfe7dc352907fc980b868725387e982b8ddf46d054df8e6dd54a4248a7bf7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842c0493d76dec0410d428de8b94c00a8", "guid": "bfdfe7dc352907fc980b868725387e98a0be07ca2e426e3bc60d30898d361288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ded175475717b62c2153b63f8c45187", "guid": "bfdfe7dc352907fc980b868725387e98b3f4cf0af8ed4ae02ddeeb10e435cd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b95bf3d0771b2d68ed47b6662ab1e0", "guid": "bfdfe7dc352907fc980b868725387e9812c712ccbecb48d4ce26f86166ff880e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981546c015040b7262e24aac1f7fc3b1d6", "guid": "bfdfe7dc352907fc980b868725387e982a440638f7cb9965d090ab0c181da156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b55e8ce8fd95b6f1617439b4057eabcd", "guid": "bfdfe7dc352907fc980b868725387e9803b6b2b356410bc7bcfcc1b875c69472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afbb886b94b12ae02f49acb9ff170604", "guid": "bfdfe7dc352907fc980b868725387e98ec9c4546923a074af807de40344c27c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d1272a85fa8bb767a47446ce509cad6", "guid": "bfdfe7dc352907fc980b868725387e985cfeda817f2be498febfcb296540639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e1586a82ee890d02abb2b39fa24af40", "guid": "bfdfe7dc352907fc980b868725387e982082b1aebfa678d630e6c739c18a5003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ddba6c96c38ddae798e7f1ee145326e", "guid": "bfdfe7dc352907fc980b868725387e987c4d107baf407af054f8312cf0ad4178"}], "guid": "bfdfe7dc352907fc980b868725387e98239213265a3abaa90df058a8c1bb91cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9840e570f7d28f64055b1dab9dc749246d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0e87b873ad96ad0a06a562ccbc5f5", "guid": "bfdfe7dc352907fc980b868725387e98192e2db6ac44b70d83d52bc9a34503d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e98f0a88717dff9f0a44bc37f82f0d7d1f4"}], "guid": "bfdfe7dc352907fc980b868725387e98819fd04ebca59115ef049e04b7b72408", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c2f740ef6174a43ddde75c8d424f6f2", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e984c4b1b52a05e595bcc1230a93d57ff06", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}