{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7a0791b206e2ee5c1aa151fdf7e3030", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982ce56c6885caca31c30bd71641c38f3e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987629a1c96831e47571ebe8eb88789eb9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985b952772205cc43831d4314b3810cc3d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987629a1c96831e47571ebe8eb88789eb9", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982717f00161c5fd6023f2cd63f1af87b9", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982b41f35f21427c4225356027fbc0db43", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9864aac37d04b59f71d84411c4c1aa7eb8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d87dc0bfa58b59a43158bd4b964ade99", "guid": "bfdfe7dc352907fc980b868725387e98b6aeb7dc2963c8bf227fe715e6eaa12a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215817ac3e5e30c99139efb3aed0a39f", "guid": "bfdfe7dc352907fc980b868725387e98fc4afed76a6ab1d1fe9e2fe612a66c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1e12d032786fbfacf6fc6fe7b3f244", "guid": "bfdfe7dc352907fc980b868725387e986b660838cd8adffafe9638329351fbb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981281f19ac934074541277b8ae5593ea4", "guid": "bfdfe7dc352907fc980b868725387e9843169ea7ff2c699b4d3af23abc02494b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e3ff17830fe49ea5c9ea8379aa52d00", "guid": "bfdfe7dc352907fc980b868725387e98eafe1aca1bca5f6dfaf3a5e8ebd99d03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889746fceb5003a1614963bf98423b1c4", "guid": "bfdfe7dc352907fc980b868725387e9874491ad33326457647309ac197dbfb32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4c939b3597d514c6e27943e19f9c624", "guid": "bfdfe7dc352907fc980b868725387e98d1e51203e39573ddaadda91d3efcc046"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891b08f48602749f5108f65d39cf75bad", "guid": "bfdfe7dc352907fc980b868725387e989e7e9df62a0c0fc3ec18693a32b9777f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56bdb7dd3c0d8a3ec14e0027b87fbc9", "guid": "bfdfe7dc352907fc980b868725387e988e4b880291107c6050b0b97a4de2d00a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92e4c7b4aa6044c4e25ce18e9d6b0e4", "guid": "bfdfe7dc352907fc980b868725387e98a35f5bfdcaa8c558ccdc295b95f9a33e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183978ee3302ec7676ee40efaec39986", "guid": "bfdfe7dc352907fc980b868725387e985c50738d99d13b4b67df3c2fcc280b61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf301e9b0d3807cf8630eb08e3a70a19", "guid": "bfdfe7dc352907fc980b868725387e9892d31e4c23e6010ddeba7f7cd29c8291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a656899424528489e69b233e0fd4b5a1", "guid": "bfdfe7dc352907fc980b868725387e98b7215974e578f4dbc0c5ffa506a26758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986773dd1b990d74cb0db7bf13e88bad90", "guid": "bfdfe7dc352907fc980b868725387e980133f0e21141757bbf4e69d0d9ac56c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98790116fa32dd081257ca608a5de4b5c0", "guid": "bfdfe7dc352907fc980b868725387e98db232a5ee3957d60b8461c11f883a5ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acac7d5f9c5f49d6f563cbc5dfd00d76", "guid": "bfdfe7dc352907fc980b868725387e98d2d09e34b0f81dac45616867f64d7a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2c1c169d220fe57137d2b52c0290f5", "guid": "bfdfe7dc352907fc980b868725387e981809ba8cac0ef4676ecb53846f586aef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf410ef2c62528ea26c23f418df2d0e9", "guid": "bfdfe7dc352907fc980b868725387e980d7a1dae395d13abcd4287d5ca32af56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f2c9bebc3ca826fe3b6c2d333ecd006", "guid": "bfdfe7dc352907fc980b868725387e984c788c9d9941b076d25081501ee97cc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2a27a5e85d575945c5aea3ea12dfc0", "guid": "bfdfe7dc352907fc980b868725387e9876cbc79df110da9314a7eea75fdbfcdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263f1a5736af2febbdbc44b6182db373", "guid": "bfdfe7dc352907fc980b868725387e98b4098e992169e2afb5f660e7315d880b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908ee787a5207e8721f1a9eba343fd06", "guid": "bfdfe7dc352907fc980b868725387e98d4593c3d3d5f1824cb7aa1b7902c5375"}], "guid": "bfdfe7dc352907fc980b868725387e9800290f8f18e0384aa6ed341e855466fb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}