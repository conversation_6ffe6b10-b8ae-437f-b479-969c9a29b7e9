{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7e956ad7e4a5b074f5fa75b3bc70be7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98597be83a97d02e5bd43d1c62106036ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3a29dfbfa833f5bb2b527b0c9eef3fc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da514c7b600946f08ba0e2d623d02b2d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b3a29dfbfa833f5bb2b527b0c9eef3fc", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982da1cf9e7f8afe0ad9e07b439a53ab4b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985d7269d071186bde5fb674d617100624", "guid": "bfdfe7dc352907fc980b868725387e98319f8192c62b4a7ee4b19426e8c52496", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98119bd9ba9868f0d3f1b969ec7c0b71a5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983391739169aa83623eac8a62325d6f64", "guid": "bfdfe7dc352907fc980b868725387e98819738c813b1f6f22daffa6fdca77f34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8468ce6ff56c6352e4a7dc3ea960349", "guid": "bfdfe7dc352907fc980b868725387e983b6e37dfb24baf60709781fb5f62c6fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9060ac993f4078236a5bcd5b0c92ca9", "guid": "bfdfe7dc352907fc980b868725387e9882e9f73ef0665499b6aff7935b3c22cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a33328ba6d01916c7644b76b04781b2d", "guid": "bfdfe7dc352907fc980b868725387e98943f02b0a1f57e7800602719feae61e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6dc3f9373378dbd00ee016b51c91ce9", "guid": "bfdfe7dc352907fc980b868725387e9832cbe2e5379e871244801289a51c8c0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a358d5cd124872e12489ab4c38eb3055", "guid": "bfdfe7dc352907fc980b868725387e9836fcb1fa5ad81ba6a3196bb40d481e45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988974597372b2dde2ef449cf5234df8dd", "guid": "bfdfe7dc352907fc980b868725387e98972a5c54955022fa1b401189766a500e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e07fe32dca444b6948c58ddc5ee70d", "guid": "bfdfe7dc352907fc980b868725387e98f1c1dbb771e56ad0a8bfba0287c7a8ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f605d7c36a4784743992a18ee8ac957b", "guid": "bfdfe7dc352907fc980b868725387e98e130abe88fe177a2460b7b2cd4c02a58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801222a0679497fca9137fdc203f66e0e", "guid": "bfdfe7dc352907fc980b868725387e9822e2925249fdfcf786c50b1ebfd8337c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b88652f18421f48d9d4c38a8be74fca2", "guid": "bfdfe7dc352907fc980b868725387e98c757a150ab7002a274d66e282f15a016"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bb7dc12ae8a7e4bbf00275198b2dab", "guid": "bfdfe7dc352907fc980b868725387e98032b6a4740dfdc6e44b1b01e9bbaf583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0329f851063bfa9756187737fe34b2", "guid": "bfdfe7dc352907fc980b868725387e98704b296bbde43a0fdf0043b865f8b2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca646e0495c32a363ea377fa0adcb7c", "guid": "bfdfe7dc352907fc980b868725387e98ab9e4843bb056a82968466416d6619bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870c92a113eff5ab9084b297dddc46d07", "guid": "bfdfe7dc352907fc980b868725387e98f719739d12eaae3be6266274d13211ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ed9ca7a075c9723c699ac2900560c3", "guid": "bfdfe7dc352907fc980b868725387e981e324a07f015519f99aadeb476611032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878f389c7ebaaacb9770adb76c34a8361", "guid": "bfdfe7dc352907fc980b868725387e98e750cbdbba7fc7c9a0b91514b3dc99dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb28c41e7a031f337e391a863873ba58", "guid": "bfdfe7dc352907fc980b868725387e9808dde19f5232466b7e458da08f8017c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f5ceb822419f8fcbaa33116daa2a7d", "guid": "bfdfe7dc352907fc980b868725387e98596da127f53a7fe66290de20233c39f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed79f87e87de58f459318bf21d78ecb4", "guid": "bfdfe7dc352907fc980b868725387e982db4b404d3d4784c1a3a3e5a204e750f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5ec9fb886ee36fcd9fc547ec59c377e", "guid": "bfdfe7dc352907fc980b868725387e985c0dfd606f524b942c84694f0b5fec1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d3f9845180a065178685a40bf7dc1d", "guid": "bfdfe7dc352907fc980b868725387e98e8fa66f289f2afb038b65edfbe2a3cce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983693d0fffd65b99bb888a8dd866cc03f", "guid": "bfdfe7dc352907fc980b868725387e989a42bb43596b6a4fc9c68292947e160d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb57a83a6546527e678f82e836dcfcf", "guid": "bfdfe7dc352907fc980b868725387e980b0d7d38d5bbfec94e5cf971d87ab610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986afeede93019bf522b5fd0be74480746", "guid": "bfdfe7dc352907fc980b868725387e9860f8c9ef586c2ab8341e0d6b5263d792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984023e66d9f8672ec2cac6b037cd0a960", "guid": "bfdfe7dc352907fc980b868725387e98c0383c4c4a388512c9237cbc0188c8ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc2a293eb21d8e324d7fa336d5e0927", "guid": "bfdfe7dc352907fc980b868725387e9898300178f0c27c74ee5bdcdeb26434ee"}], "guid": "bfdfe7dc352907fc980b868725387e988e8ed6decb93a3b5f35460c44e1940b9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98709db05cbcba8a38b5fff878acd3dc96", "guid": "bfdfe7dc352907fc980b868725387e98be003a964f60c971a0ba608fcb8310aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296ed536311979929249249546219ab", "guid": "bfdfe7dc352907fc980b868725387e9887828f2b69d9d140a5ddc4ee541af4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9835333a5e04dd0557d0d41121ecb9d386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0e87b873ad96ad0a06a562ccbc5f5", "guid": "bfdfe7dc352907fc980b868725387e984cd217202e42e9b1876f622c168c3bbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e982eb366cf39ad20479829ea5491e315fc"}], "guid": "bfdfe7dc352907fc980b868725387e98d060e2e810a6c6bac66390dd1c2df73b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987b08b7029f66706f143d14b2600f5ee4", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e985d5534f8ed80c6f80c5bc9598c3f148e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}