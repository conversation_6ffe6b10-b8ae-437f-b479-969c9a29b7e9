{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98809d3f48feaad211adc982cddd01bd6d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc6084da4002b40a4ab53c6e31bd99b2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a2575ab4cd1eaef27fbed53516bacfa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c4ffe2c19bb42a915e3a3fcdebe293", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a2575ab4cd1eaef27fbed53516bacfa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982ca23ddd0ed1c9580e34915e3f08384f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e51b359037ff55c978e8497d5cfbcf31", "guid": "bfdfe7dc352907fc980b868725387e987141c639b27ede8ae2e3ddc2e8ddf0a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c1726dbf38dc7ea3752f0b24af64cd4", "guid": "bfdfe7dc352907fc980b868725387e983288101457ace088d8a7a9ba6feac33e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988917d7cf14c759eb7d04b5b42f483500", "guid": "bfdfe7dc352907fc980b868725387e9842760d47c05cc9eb0a98f74f44480895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986816cd2b52d20d9fc4ea74b62831742f", "guid": "bfdfe7dc352907fc980b868725387e98a444e17592c02f44cab9ad2c7726869c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89dda13b8c7d554ef61f159b8bb3025", "guid": "bfdfe7dc352907fc980b868725387e98fd3f5a3cfbb47c8e424b494a8b100065"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986838221e6fb0d9823cc8d3d82c0e74a4", "guid": "bfdfe7dc352907fc980b868725387e985919ca70e7b7cfe48ac552c9ada3a5e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee4961c5fad700cb43bb606a176921a", "guid": "bfdfe7dc352907fc980b868725387e9816405eb32f93ff98e3dbcdce0f07cfb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d14a55036f7df7f05806a642e3b4755", "guid": "bfdfe7dc352907fc980b868725387e981e6dc058d767c71820dbe8207c74d129"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ed3b057d6aff5d76fe9c6d35fb6b2b8", "guid": "bfdfe7dc352907fc980b868725387e989afd248decc009cf56d9d002317c472a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea7464d2718b27f2a9b16a0451581fe6", "guid": "bfdfe7dc352907fc980b868725387e984f7dbba6c339d6036ca7d56f0cb59fe8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7d25058fe3c60145a0c57c858e50f1", "guid": "bfdfe7dc352907fc980b868725387e986ee905a1c6ad6dbb0c43ad1fdcde5008", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef555d9cdbdc351741d643f0bc1d7142", "guid": "bfdfe7dc352907fc980b868725387e9895c2f01fec217b5e9c6b5825938437de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f872b92ccbf08e72ed8a09a549c38681", "guid": "bfdfe7dc352907fc980b868725387e981d7bb97e1cb728cef2118666de696e6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18b73ed02c2c5037cfe5ed74c5da5f2", "guid": "bfdfe7dc352907fc980b868725387e98f353130cc3648c4c9821d6ce80a8ab38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b80ccc183212dd5ec1b53af5704744", "guid": "bfdfe7dc352907fc980b868725387e98ed937fc5136e61b3baacb9916d89e254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75899e15e60079514a1299a99e4d8ef", "guid": "bfdfe7dc352907fc980b868725387e98438d23011764aa9609d1524d5982bb2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c718c4dd7f768e40c5bae07b406cb8b", "guid": "bfdfe7dc352907fc980b868725387e989d3187ae83840b704d235a735947f8d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a96ff60bea74ceb583ebf533cc0df9f", "guid": "bfdfe7dc352907fc980b868725387e9820d24025246a663ea43329a360940b36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b2823e9e494013ac6e76df9cb644717", "guid": "bfdfe7dc352907fc980b868725387e9874ad0823fedc30f2911914b515ff09c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863f2dc7b8554afc9e8e51a46a756a4d2", "guid": "bfdfe7dc352907fc980b868725387e982745c16da05436da394fdd9d51048b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b6b39750c5a69b1012912cfa3e7ac7", "guid": "bfdfe7dc352907fc980b868725387e980e790e36e0145fd7a4a83194704ed274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a048e6ae57abb176ba174faad4a370", "guid": "bfdfe7dc352907fc980b868725387e9803aab9b6b70085d94eaf3ecd15dacae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d3eb39462a01e27f7e1c11b83baa3a9", "guid": "bfdfe7dc352907fc980b868725387e987594a0b2bf3111c41b016e56adae6bb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c5f65be8821ddf21f327f2b03a6830", "guid": "bfdfe7dc352907fc980b868725387e98376a73a60554634445a74de86e0cac6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb3a03a6ce06f6379a4b19579c7bde9", "guid": "bfdfe7dc352907fc980b868725387e9800f72681f8e23f7e73973cf1396b80f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e70f7f554b8de5a368626f37f68f9b79", "guid": "bfdfe7dc352907fc980b868725387e9865fa8294d5d5f5809bfe5bef05de9caf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8fe18b4ff7727ba27ecc8ae70065cd6", "guid": "bfdfe7dc352907fc980b868725387e981e53e6971db6278973df1b5584ad8454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b33960c376b34536bb2815bb985236a", "guid": "bfdfe7dc352907fc980b868725387e9858a729646e7ae6b69df065c6ca6774aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3925df3a8075320ad5243af03d3158b", "guid": "bfdfe7dc352907fc980b868725387e98aa36d8416f0f25b2831ecbfc87953822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c53f0408b2dfe8401cbbabf8fd201be", "guid": "bfdfe7dc352907fc980b868725387e98d4a1c03d0b6ddbd3540d520eb0f32f09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0a1eaf474da6abca12ea975f8cac338", "guid": "bfdfe7dc352907fc980b868725387e980887368747c686641808d5402e77cb33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9e99589d1a8fadbb9c92f3cf02d29c0", "guid": "bfdfe7dc352907fc980b868725387e98f4ef750ccd3e46018af3350335e789de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981deae75b8ac5ce195306fe5602aca0b4", "guid": "bfdfe7dc352907fc980b868725387e98a875068982b43dc1d9b18341586e18ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8dcf46650934ebbc809b19f93e28d0", "guid": "bfdfe7dc352907fc980b868725387e985d4c64924b30e4f26bcf917370e91724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32b300a2eb171c59c05219619bb30da", "guid": "bfdfe7dc352907fc980b868725387e98861ebf5a252c0342498e279cea99d684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986278e728cd8c1e31168e52bff2ae4755", "guid": "bfdfe7dc352907fc980b868725387e98fa09414fd19dc1f02bd0108f4ebee3ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810cf8fd504f55a8e1ff9cba371c649d2", "guid": "bfdfe7dc352907fc980b868725387e98c0c0e813e16b47628edd3da9f743bfb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae71bc144e9e2bd4868499f09e326e27", "guid": "bfdfe7dc352907fc980b868725387e983db290384aef8126ccc818fcfda7d0bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d51a9e9873127935f897bc83988ea4", "guid": "bfdfe7dc352907fc980b868725387e98ad7af0ec3020b49203111bad0ff4e396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873a682e597a1b0fe873a7c58346f610f", "guid": "bfdfe7dc352907fc980b868725387e98b1b1abcb8c13a809ef881687cd7cac91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdcd3a7d3131c3c1093fa222b24a73ba", "guid": "bfdfe7dc352907fc980b868725387e98c37948a1726f02473c3a9383333209c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8735f0b16a091961900c67d955701a", "guid": "bfdfe7dc352907fc980b868725387e9884bc4bd78e1ec80a536c8fe65a35b3e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98710c0c8ea11c79c111c81b4ee69e9e88", "guid": "bfdfe7dc352907fc980b868725387e989ef9a0b2c7b302082d093ec6ec0da52f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c91c5e14b91b2d755442f8f4799074d6", "guid": "bfdfe7dc352907fc980b868725387e98c957fc5e1bd2dacf23aa5638eac3ebf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6515ce9fad8dc04939c16f7a7638513", "guid": "bfdfe7dc352907fc980b868725387e982f731905bc97856316a536114728dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e116891a6c7515331473d32812e4dc73", "guid": "bfdfe7dc352907fc980b868725387e985a438f02753da437a697b9950e31a8ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981938666e5e8b511e950ca21080d7a644", "guid": "bfdfe7dc352907fc980b868725387e98b20bbcd035d92c15a7ec1e719a7e4f39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982346516fee15f157995c4fbb6c533c69", "guid": "bfdfe7dc352907fc980b868725387e98bbac24d7df9e08b6ce5dcef27044793e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9dce61e152c997929ef8529082d0545", "guid": "bfdfe7dc352907fc980b868725387e98096a09d89c0ec2bf47bded9a159aad7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876a5341a47631a0e047d6e1843b45159", "guid": "bfdfe7dc352907fc980b868725387e9898ccd484579758d2a573a73e101c6c46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e157cc5bfe64bb95b3fb12a536d42a3e", "guid": "bfdfe7dc352907fc980b868725387e98ecbfe54d28177b6efa717945a99d135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf75edd78dad2fc9134c0dec85ed941", "guid": "bfdfe7dc352907fc980b868725387e98afa0ccac061bf97ad8a59ebc2bf72d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4c5dadb8f038e251e24942b2533aed4", "guid": "bfdfe7dc352907fc980b868725387e987ba1e2530fa9d0cfb7c2d3f637bf1491"}], "guid": "bfdfe7dc352907fc980b868725387e982e4bfd62dc847046c93cf30d7a192e63", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9853b63b43d5ca77fff549bdc55b72375a", "guid": "bfdfe7dc352907fc980b868725387e981fb2dddb2d237fdb543aaa79f202af8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984acd94cc58cea6a73cc81e27f1ac0589", "guid": "bfdfe7dc352907fc980b868725387e9886dc60b479df9d07b87d011fdac58a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c5d31893ed2f4eec37e041c4ed8c93", "guid": "bfdfe7dc352907fc980b868725387e98cd7078559bd40bba7448b9dba01befc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc6f0a32c749e289bfa1d8a1d2d0f63", "guid": "bfdfe7dc352907fc980b868725387e981432cc7e21db53e123b252b721d996ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f709baeb6f8acce953373078d469f3ca", "guid": "bfdfe7dc352907fc980b868725387e984507b06ba377a0d7f274c84371a8c400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b1dbc02a3daead925c987a2110580c", "guid": "bfdfe7dc352907fc980b868725387e98f9dfa88692b8c0ff4bf2827aea924579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865fb5e96da29e1cab8e23471cca06d05", "guid": "bfdfe7dc352907fc980b868725387e98aca3cca9be1a346513f210a7224055ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c3412994d66e2459d4f21da09efb94", "guid": "bfdfe7dc352907fc980b868725387e98e73c8b3f0058d998ebc762b151a88174"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7c73523aea06e4871a03faefdac784", "guid": "bfdfe7dc352907fc980b868725387e982be8e35348537fc868d7f75b6140ae71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61d5e2aa4dee3f2fbf186450ea912e6", "guid": "bfdfe7dc352907fc980b868725387e9871d77ecf64188dcc7e7f4ac2870d1a75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986351bf9b1930f6b6aa1af7bb43f6f025", "guid": "bfdfe7dc352907fc980b868725387e988ab33c952ad476a5d90821d0b69de6d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5af5a71d83c37452c1f2948b74dc5eb", "guid": "bfdfe7dc352907fc980b868725387e98815f24a924a29fa112166ccaef20d7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818fcf2101c6236ffeb02d682116a16ff", "guid": "bfdfe7dc352907fc980b868725387e98093c0b296860d6a076171c904540e64f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71c10f13f8df6a9b6a994bca21eae2e", "guid": "bfdfe7dc352907fc980b868725387e98b92fe011b4575799211e2d7acb153e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a64fa075263d903492ad350eda3616c3", "guid": "bfdfe7dc352907fc980b868725387e98cecda210767c5cb49b25ca81c52adae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd7c80a28511fb3ce3e8703ab77a8694", "guid": "bfdfe7dc352907fc980b868725387e9875fda92749a87dcf4ee2e69c941c163c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98234b73e34fe61478202fd40979b7269e", "guid": "bfdfe7dc352907fc980b868725387e98c9c155b18dd5b9a6c77ae9b6cfe3cbc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee61035ac0ae4b84005d3cdb5e72074", "guid": "bfdfe7dc352907fc980b868725387e98e30dc9ac12702f12646c31a386005ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ce4005eff32043b10cf8489a4c518b1", "guid": "bfdfe7dc352907fc980b868725387e98e147cbd9dc02186a11097ef1fe011b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d607f3c793b4e16aff2bee516879f28", "guid": "bfdfe7dc352907fc980b868725387e98a6ac199daf9510a84ddeb78fb365b722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981adb4d33c06b298bc566ecd64a016c69", "guid": "bfdfe7dc352907fc980b868725387e9878a8d26bd83c6280e9ce8ee38fd12339"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881ccb089b169820c8524062d9922f390", "guid": "bfdfe7dc352907fc980b868725387e9851d8aff52d45ed963ce834b2176792da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc60073f7edfce51853f6102696791f", "guid": "bfdfe7dc352907fc980b868725387e98769bc85a2f3444121b63d89ff799e760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e307fe0e9476f8fef6e0779a17c19bf", "guid": "bfdfe7dc352907fc980b868725387e98583d37377b2d2b13c9e8fbda8d60d0a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccf721b9e7e5b356843c37db3282f0a0", "guid": "bfdfe7dc352907fc980b868725387e98dead72ae6960d0581650d83843fc7252"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f22016808f648fd5b87206929d15e8de", "guid": "bfdfe7dc352907fc980b868725387e9811acddeb19b9b6c44db58c9e9798fe56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a64cd71273c2485bec6c716c97b7f2a4", "guid": "bfdfe7dc352907fc980b868725387e98a17883d889149f11dec902ea802437b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbb27991552d52e11f167d1317e3f2cb", "guid": "bfdfe7dc352907fc980b868725387e982f6235195d4083a8997d251ca0141dfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398e3f4ddfea90c291051d7aff1ce416", "guid": "bfdfe7dc352907fc980b868725387e988582d8f01787af28313e27c8f7e93b2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed545e619efb0d9fb8a429272a9e8c5b", "guid": "bfdfe7dc352907fc980b868725387e984cb059fddcc05e08679624d2f35a701f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98563d7225cf5fe160d0a1ae64d0f04306", "guid": "bfdfe7dc352907fc980b868725387e9800d0e8cefd592540b57fe028cc84395e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987584e8a2a21db5036acae08a8fdb3dd0", "guid": "bfdfe7dc352907fc980b868725387e98d10a514899510dfc0148354a2e6c3f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b33a42b8af42617cabfc122f0a6154", "guid": "bfdfe7dc352907fc980b868725387e98463eb6ce885a6c0a2e3e302460d5fd48"}], "guid": "bfdfe7dc352907fc980b868725387e98b1ddf3b0e29d270e33967a6775ba643d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e989f0204fa616f73e247cdf4121cdd297f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e98a15d33935918d21450a7de93f4fce9b0"}], "guid": "bfdfe7dc352907fc980b868725387e98e3230d98ea381c28bbab2be4bff4a57c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988886d72ea4d38a5faf46fa388095ef4e", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e9881c0217f8f1c5fc2101b4efe74f4ed15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}