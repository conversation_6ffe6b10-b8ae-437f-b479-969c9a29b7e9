{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98502b9c7fde8b64cb915f85b0e1859567", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a752cf25e0dfe7b1fb53af4dd74d258f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9558d8d5f213aeb9fbff6acca9d88f2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802efd6b809cf420935851c95432c642b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e9558d8d5f213aeb9fbff6acca9d88f2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985213b5b786db6f9c6bbf506e430c0840", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988559c078b25a45d38999b0e714a2d333", "guid": "bfdfe7dc352907fc980b868725387e986b1cadeebea693cddca91edc6eb88993", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1b0f61d433cdd4ea0bd4d1f5c4e67e", "guid": "bfdfe7dc352907fc980b868725387e987158f973ac315f2312799f72e29ce9c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98433dd5a99bed96c93627712b5301564d", "guid": "bfdfe7dc352907fc980b868725387e98d61032a05330ed2ee49f241561dc2780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bca7054ebd403713066c7fac4b249b4", "guid": "bfdfe7dc352907fc980b868725387e984b7608730253d5f793035f0833bd5c7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836314e115f3b71aff42932a123384d69", "guid": "bfdfe7dc352907fc980b868725387e9842055ebed45a32e66f7b8b0a7a2a4105", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed23ab6a84e5bf73170c2d546adeed7a", "guid": "bfdfe7dc352907fc980b868725387e984116bfbfa0ab9420b80da563cfe01904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b33d0e949e3621a6fdd4c48fbdae575", "guid": "bfdfe7dc352907fc980b868725387e98db4d0f84c26c2468194cf1bf252e28d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c55599f234f6079c5abf68f393338f", "guid": "bfdfe7dc352907fc980b868725387e986800b093a8f6948448d5f170c3e5180e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3cd42fe8c065eb5f3d1997854ffa42c", "guid": "bfdfe7dc352907fc980b868725387e98d3b56b9a8452c29d6128e134627bd447", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d7b03e86a72d28b49505e5b4040bd06", "guid": "bfdfe7dc352907fc980b868725387e98d3d8f30778c87b42b440d5bb9b848afb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec085f206483a43450a103801d3dbe4", "guid": "bfdfe7dc352907fc980b868725387e98f23e497836b8f319223777095317d074", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f2894275173b53ca879f461f27a5fd7", "guid": "bfdfe7dc352907fc980b868725387e98037f79cfad111c77be423807172ef625", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e5a084bef59feef00e2551820f5d79e", "guid": "bfdfe7dc352907fc980b868725387e98ca23a693ec41d1395ffd370d0415d7ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988198f6e343a0ec048637dc9c5a261a1f", "guid": "bfdfe7dc352907fc980b868725387e98fc28695a2ac3cf9d88f9a36da2f9543e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98295ec5753e6bcf2a7fe796a11253b2e1", "guid": "bfdfe7dc352907fc980b868725387e986a2e51f8374a81ae0311623e1344ba46", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980039c994168f1194df8c64b8165d509b", "guid": "bfdfe7dc352907fc980b868725387e98d83896785622e79a8158b596a244ffa9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a6f5e9ee12cc4de783ba9ffeff123a", "guid": "bfdfe7dc352907fc980b868725387e985313c6f56d1f6aa4b9ceadec7c5ac3db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e92cd2dd5f36d4c05aa91c5727fbda", "guid": "bfdfe7dc352907fc980b868725387e9800ab496c9139ba25149590364c48c555", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845b0d0838794cc8b2d0a44769d20f588", "guid": "bfdfe7dc352907fc980b868725387e9841eaecbef5ab4c8e77fb7f396d8dab12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edbc5c60c06f78a316b40a8a989ae62f", "guid": "bfdfe7dc352907fc980b868725387e9895c001463dbe3b3fdd70c4d86cd16d54", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825cef24614fe00a49a769b0ee167fab8", "guid": "bfdfe7dc352907fc980b868725387e98b254eafa43ffa69ae0d95768fc082cbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2fe38dd519d1338042cf1acffa745cb", "guid": "bfdfe7dc352907fc980b868725387e98939dbd5fb516f92a5d38803fca7fa89a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a39f263cd13003db22ef4c691e06df21", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f66691958166958dad55b25790faf086", "guid": "bfdfe7dc352907fc980b868725387e9836361257d60a720c8bdf3c1107427340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df91f64ef834a5160dc362af7b0d64a3", "guid": "bfdfe7dc352907fc980b868725387e9885e5e102d123b8ca2cd40f9815f571b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f4baabc5fb7aa4d73e66047b9b3412", "guid": "bfdfe7dc352907fc980b868725387e98f6d280d6eddf847f6f5dbc1e2f9b2718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b1e62cc7318c7143109d626c8e3310", "guid": "bfdfe7dc352907fc980b868725387e987bd2d7b71f2357ca53f309f39cdca1ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812ae90688f355654b7651e1fa0496f19", "guid": "bfdfe7dc352907fc980b868725387e986353b6e44ea5df02875fcdbaf7f36273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41ca1e9e08eb0b39aae67a17f740d5a", "guid": "bfdfe7dc352907fc980b868725387e982eeb582dd6c3dec3891e6418d05a8b9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d74c90cef78f7073c1859b4c6e461c30", "guid": "bfdfe7dc352907fc980b868725387e981b11af5280c9b7a9b6b73e1229b1667b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982035c1c1d3ee7bf07052eaface2780ea", "guid": "bfdfe7dc352907fc980b868725387e9851d32b45c6ceb8c4ba2041a6f2d262c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56fb264e7c65b9e2be34771f09ca29b", "guid": "bfdfe7dc352907fc980b868725387e985b7f7194946d4b2cc8314be5fce820af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dbbb69cc779408ce786de1b1c5c8636", "guid": "bfdfe7dc352907fc980b868725387e98aed23530388e49f944185a76c2e4041d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f275904748488ddbe3ae3cdcf0fea3f5", "guid": "bfdfe7dc352907fc980b868725387e981d7c697b1abcdc72813f346d2118f9aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98736876083df1a955cc72e3f17ccc09c7", "guid": "bfdfe7dc352907fc980b868725387e9834239cbd40d770de43f5b3d9fe1dcf6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98070d8a40deabe4949e6308b780b91305", "guid": "bfdfe7dc352907fc980b868725387e98f18f6e314900db18e3c89ed75d59994f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983981ae57710a0c2ed43f8d2ee7c0e5d5", "guid": "bfdfe7dc352907fc980b868725387e980b79e5657d512b97a1fb674c4041bf19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9260c2756c17bb3800cc8a357a94d9", "guid": "bfdfe7dc352907fc980b868725387e9899a49b1cd3efb5f3f4fbe789b80c5e09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4bcd44af21bd0687a978bcff9a1e976", "guid": "bfdfe7dc352907fc980b868725387e98d5b1c3642f363f00d1858cf2a4f07ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983999d597ea031ff581009f279c641e57", "guid": "bfdfe7dc352907fc980b868725387e98368c8a686a929b0ccec28594d80c3ab9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e391fad09da5336ce252aaa9c88a9164", "guid": "bfdfe7dc352907fc980b868725387e987a533c31827e8f0f9c452ccb0566d09d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988caf99880cde25afca35ad17952390b8", "guid": "bfdfe7dc352907fc980b868725387e98a8b9f574231760ba6bce6c5461966ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a2f1ef028f090ffa42dad83b723e732", "guid": "bfdfe7dc352907fc980b868725387e98246bd0bbb839be8ff07723ca3a2c54bf"}], "guid": "bfdfe7dc352907fc980b868725387e981745c2c45e038a70a66250bfe84bddfb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e983350faeb0aa51bb103b1c5d480ac5de6"}], "guid": "bfdfe7dc352907fc980b868725387e9886c7626b856509049bf1b80ceab9ac99", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985facf2219c06080cba6d736b89d1855f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98373d2698dece314b5889fb5b757743d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}