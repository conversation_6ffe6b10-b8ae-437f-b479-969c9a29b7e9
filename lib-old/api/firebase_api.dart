import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/categoryListScreen/attendance.dart';
import 'package:silverleaf/view/categoryListScreen/notification_panel.dart'
    as panel;

import 'package:shared_preferences/shared_preferences.dart';

import 'package:http/http.dart' as http;
import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'dart:convert';
import 'dart:async';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'dart:io' show Platform;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

// This function handles messages when the app is in the background
Future<void> handleBackgroundMessage(RemoteMessage message) async {
  // print('Title:${message.notification?.title}');
  // print('body:${message.notification?.body}');
  // print('Payload:${message.data}');
}

class FirebaseApi {
  List userNames = [];
  final firebaseMessaging = FirebaseMessaging.instance;

  // Local notifications plugin for handling foreground notifications
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // AndroidNotificationChannel for Android devices
  final AndroidNotificationChannel channel = const AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.high,
  );

  Auth_Users() async {
    print('aurth uses');

    var user;
    var server_key;
    var token;
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames.length > 0) {
      user = storedUserNames[0];
      token = prefs.getString('token');

      // Skip server update if token is null or empty
      if (token == null || token.isEmpty) {
        print('Token is null or empty, skipping server update');
        return;
      }

      server_key =
          'AAAA46X6ERg:APA91bE2ISN1zLKY_d-E2P8xQJ-YIVmbuNBpH0xq9zDtgQ1fm58nFauNZuB8vkw61tYAnM4xwrs74BUbeNrqb27Am0zUxM00aWRs5wPvxNX4h65Rhxjeexu0ZIS8KpTe4NtyajpH-cTJ';

      final url =
          'https://silverleafms.in/silvar_leaf/api/notification/save-token';
      final data = {
        'server_key': server_key,
        'token': token, // Use the non-null token
        'user_id': user ?? '',
        'type': '3',
        'class_id': storedUserNames.length > 6 ? storedUserNames[6] : '',
        'branch_id': storedUserNames.length > 7 ? storedUserNames[7] : '',
        'primary': storedUserNames.length > 12 ? storedUserNames[12] : '',
        'secondary': storedUserNames.length > 13 ? storedUserNames[13] : '',
      };

      try {
        print('api response data');
        final response = await http.post(Uri.parse(url), body: data);
        print("api response body");
        print(response.body);

        // Check if the response is valid JSON
        if (response.statusCode == 200) {
          try {
            final result = json.decode(response.body);
            if (result['status'] != '' &&
                result['status'].toString() == 'success') {
              print(result);
            } else {
              print(result);
            }
          } catch (e) {
            print('Error parsing response: $e');
            // Response is not valid JSON, possibly HTML error page
            if (response.body.contains('Integrity constraint violation')) {
              print('Database constraint error detected');
            }
          }
        } else {
          print('Server returned status code: ${response.statusCode}');
        }
      } catch (e) {
        print('Network error when updating token: $e');
      }
    }
  }

  multiple_user(class_id, branch_id, user_id) async {
    print('api response record all');
    print(user_id);

    final prefs = await SharedPreferences.getInstance();
    var token = prefs.getString('token');

    // Skip server update if token is null or empty
    if (token == null || token.isEmpty) {
      print('Token is null or empty, skipping server update in multiple_user');
      return;
    }

    var server_key =
        'AAAA46X6ERg:APA91bE2ISN1zLKY_d-E2P8xQJ-YIVmbuNBpH0xq9zDtgQ1fm58nFauNZuB8vkw61tYAnM4xwrs74BUbeNrqb27Am0zUxM00aWRs5wPvxNX4h65Rhxjeexu0ZIS8KpTe4NtyajpH-cTJ';

    final url =
        'https://silverleafms.in/silvar_leaf/api/notification/save-token';
    final data = {
      'server_key': server_key,
      'token': token,
      'user_id': user_id,
      'type': '3',
      'class_id': class_id,
      'branch_id': branch_id,
      'mobile': branch_id,
    };

    try {
      final response = await http.post(Uri.parse(url), body: data);
      if (response.statusCode == 200) {
        try {
          final result = json.decode(response.body);
          if (result['status'] != '' &&
              result['status'].toString() == 'success') {
            print(result);
          } else {
            print(result);
          }
        } catch (e) {
          print('Error parsing response in multiple_user: $e');
          // Response is not valid JSON, possibly HTML error page
          if (response.body.contains('Integrity constraint violation')) {
            print('Database constraint error detected in multiple_user');
          }
        }
      } else {
        print('Server returned status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Network error in multiple_user: $e');
    }
  }

  Future<void> getTokenWithWorkaround() async {
    // Initialize local notifications first
    await _initLocalNotifications();

    // Request permission for both platforms
    NotificationSettings settings = await firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
      criticalAlert: false,
      announcement: false,
      carPlay: false,
    );

    print('User granted permission: ${settings.authorizationStatus}');

    // iOS-specific settings
    if (Platform.isIOS) {
      // Set foreground notification presentation options for iOS
      await firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      // Get device info to detect if we're on a physical device
      final deviceInfo = await DeviceInfoPlugin().iosInfo;
      final packageInfo = await PackageInfo.fromPlatform();

      print('App bundle ID: ${packageInfo.packageName}');
      print('Is physical device: ${deviceInfo.isPhysicalDevice}');

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('Permission granted, using workaround token for iOS');

        // Generate a device-specific mock token that remains consistent
        final String mockDeviceToken =
            'ios-device-${deviceInfo.identifierForVendor}-${packageInfo.packageName}';

        // Store the mock token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', mockDeviceToken);

        // Get user data and update server if needed
        userNames = prefs.getStringList('users') ?? [];

        print('Using mock token: $mockDeviceToken');

        if (userNames.isNotEmpty) {
          await Auth_Users();
        }

        // Register the background message handler and set up listeners
        _setupMessageHandlers();

        return;
      }
    }

    // For Android or as a fallback, try to get the FCM token normally
    try {
      final fcmtoken = await firebaseMessaging.getToken();
      print('FCM Token: $fcmtoken');

      if (fcmtoken != null) {
        // Store the token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', fcmtoken);

        // Get user data and update server
        userNames = prefs.getStringList('users') ?? [];
        if (userNames.isNotEmpty) {
          await Auth_Users();
        }
      } else {
        print('Failed to get FCM token, using fallback');

        // Generate a generic fallback token as last resort
        final String fallbackToken =
            'fallback-token-${DateTime.now().millisecondsSinceEpoch}';

        // Store the fallback token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', fallbackToken);

        // Process as usual
        userNames = prefs.getStringList('users') ?? [];
        if (userNames.isNotEmpty) {
          await Auth_Users();
        }
      }
    } catch (e) {
      print('Error in token retrieval: $e');

      // Even if there's an error, generate a fallback token
      final String emergencyToken =
          'emergency-token-${DateTime.now().millisecondsSinceEpoch}';

      // Store the emergency token
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', emergencyToken);

      // Process as usual
      userNames = prefs.getStringList('users') ?? [];
      if (userNames.isNotEmpty) {
        await Auth_Users();
      }
    }

    // Set up message handlers regardless of how we got the token
    _setupMessageHandlers();
  }

  // Helper method to set up all the message handlers
  void _setupMessageHandlers() {
    // Register the background message handler
    FirebaseMessaging.onBackgroundMessage(handleBackgroundMessage);

    // Set up message handlers for when app is opened from a notification
    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);

    // Handle messages when app is launched from terminated state
    FirebaseMessaging.instance.getInitialMessage().then(handleInitialMessage);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // Message received while app is in foreground
      print('Message received in foreground: ${message.notification?.title}');

      // Show a local notification to make it visible in foreground
      _showLocalNotification(message);
    });

    // Listen for token refreshes
    firebaseMessaging.onTokenRefresh.listen((newToken) {
      print('FCM Token refreshed: $newToken');
      // Update token in SharedPreferences
      SharedPreferences.getInstance().then((prefs) {
        prefs.setString('token', newToken);
        // Update the token on your server
        if (userNames.isNotEmpty) {
          Auth_Users();
        }
      });
    });
  }

  Future<void> _initLocalNotifications() async {
    // Create the Android notification channel
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);

    // Android initialization settings
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS specific initialization settings
    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: false, // We'll request this in FCM flow
          requestBadgePermission: false, // We'll request this in FCM flow
          requestSoundPermission: false, // We'll request this in FCM flow
        );

    // Combined initialization settings
    final InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    // Initialize the plugin
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse details) {
        // Handle notification tap on iOS and Android
        if (details.payload != null) {
          try {
            Map<String, dynamic> payloadData = json.decode(details.payload!);
            // Handle the payload similar to how you handle FCM messages
            _handleNotificationPayload(payloadData);
          } catch (e) {
            print('Error parsing local notification payload: $e');
          }
        }
      },
    );
  }

  // Handle notification payload when tapped
  void _handleNotificationPayload(Map<String, dynamic> payload) {
    // Create a structure similar to FCM RemoteMessage for consistent handling
    if (payload['type'] != null) {
      if (payload['type'] == '3') {
        // Handle chat notifications
        if (userNames.length > 0) {
          List<dynamic> body = json.decode(payload['body']);
          if (body.isNotEmpty) {
            Get.off(
              MainPageChat(
                staff_id: body[0]['id'].toString(),
                student_id: userNames[0],
                Section_name: body[0]['section_name'],
                class_name: body[0]['class_name'],
                Profile: body[0]['profile'],
                name: body[0]['name'],
                sender_id: userNames[0],
                message_count: 1,
                class_id: userNames.last == '1' ? userNames[6] : userNames[6],
                section_id: userNames.last == '1' ? userNames[5] : userNames[5],
                branch_id: userNames.last == '1' ? userNames[7] : userNames[7],
              ),
            );
          }
        }
      } else if (payload['type'] == '5') {
        // Handle diary notifications
        List<dynamic> body = json.decode(payload['body']);
        if (body.isNotEmpty) {
          var dos = body[0]['given_date'];
          Get.to(
            DairySecondScreenDetails(
              user_id: body[0]['id'],
              title: body[0]['title'],
              description: body[0]['description'],
              posted_date: body[0]['date'],
              ending_date: dos,
              url: body[0]['url'] != null ? body[0]['url'] : '',
              teacher_name:
                  body[0]['role'] == 2 ? body[0]['staff_name'] : 'Admin',
            ),
          );
        }
      } else if (payload['type'].toString() == '6') {
        Get.to(Attendance());
      } else {
        Get.to(
          panel.NotificationSecondScreenDetails(
            title: payload['title'] ?? 'Network issues',
            description: payload['body'] ?? '',
            url: payload['url'],
            user_id: '0',
          ),
        );
      }
    }
  }

  // Show local notification for foreground messages
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final RemoteNotification? notification = message.notification;

    // Ensure we have at least a title or body
    if (notification != null &&
        (notification.title != null || notification.body != null)) {
      // For Android, use the high importance channel
      final AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
            channel.id,
            channel.name,
            channelDescription: channel.description,
            importance: Importance.max,
            priority: Priority.high,
            showWhen: true,
            icon: '@mipmap/ic_launcher', // Use your custom icon if available
          );

      // For iOS
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
            // Add your custom iOS specific settings here
            sound: 'default',
            badgeNumber: 1,
            threadIdentifier: 'silverleaf_notifications',
          );

      // Combined platform details
      final NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Generate a unique ID for the notification
      final int notificationId = notification.hashCode;

      // Show the notification
      await flutterLocalNotificationsPlugin.show(
        notificationId,
        notification.title,
        notification.body,
        platformChannelSpecifics,
        payload: json.encode(
          message.data,
        ), // Pass the data as payload for handling tap
      );
    }
  }

  // Debug APNS Token method
  Future<void> debugAPNSToken() async {
    if (Platform.isIOS) {
      print('======= iOS APNS Token Debugging =======');

      // Check authorization status first
      final settings = await firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      print('Permission status: ${settings.authorizationStatus}');

      // Check device and app info
      final packageInfo = await PackageInfo.fromPlatform();
      final deviceInfo = await DeviceInfoPlugin().iosInfo;

      print('App Info:');
      print('- Bundle ID: ${packageInfo.packageName}');
      print('- App Version: ${packageInfo.version}+${packageInfo.buildNumber}');

      print('Device Info:');
      print('- Model: ${deviceInfo.model}');
      print('- System Version: ${deviceInfo.systemVersion}');
      print('- Is Simulator: ${deviceInfo.isPhysicalDevice == false}');

      // Try to get the APNS token
      print('Checking for APNS token availability...');

      String? apnsToken = await firebaseMessaging.getAPNSToken();
      print('Initial APNS token: $apnsToken');

      // Wait and try again a few times
      for (int i = 1; i <= 5; i++) {
        if (apnsToken != null) break;

        print(
          'APNS token is null, waiting and trying again (attempt $i of 5)...',
        );
        await Future.delayed(Duration(seconds: 3));

        apnsToken = await firebaseMessaging.getAPNSToken();
        print('APNS token after attempt $i: $apnsToken');
      }

      if (apnsToken == null) {
        print('Failed to get APNS token after multiple attempts');

        // Check if we can get FCM token without APNS
        try {
          final fcmToken = await firebaseMessaging.getToken();
          print('Attempted to get FCM token without valid APNS: $fcmToken');
        } catch (e) {
          print('Error getting FCM token: $e');
        }

        print('Troubleshooting steps:');
        print(
          '1. Verify that Push Notifications capability is enabled in Xcode',
        );
        print(
          '2. Check that your provisioning profile includes push notifications',
        );
        print('3. Confirm that your Apple Developer account is paid/active');
        print(
          '4. Make sure APNs certificate is properly configured in Firebase',
        );
        print('5. Test on a physical device, not a simulator');
      } else {
        print('Successfully retrieved APNS token: $apnsToken');

        // Try to get FCM token now that we have APNS
        try {
          final fcmToken = await firebaseMessaging.getToken();
          print('FCM token with valid APNS: $fcmToken');
        } catch (e) {
          print('Error getting FCM token even with APNS: $e');
        }
      }

      print('======= End of APNS Token Debugging =======');
    } else {
      print('Not an iOS device, skipping APNS token debugging');
    }
  }

  Future<void> initNotifications() async {
    if (Platform.isIOS) {
      // Run the debugging first to get more information
      await debugAPNSToken();

      // Then use the workaround method
      await getTokenWithWorkaround();
    } else {
      // For Android, use the normal approach
      // Initialize local notifications first
      await _initLocalNotifications();

      // Request permission
      NotificationSettings settings = await firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: false,
        announcement: false,
        carPlay: false,
      );

      print('User granted permission: ${settings.authorizationStatus}');

      // Get the FCM token
      final fcmtoken = await firebaseMessaging.getToken();
      print('FCM Token: $fcmtoken');

      if (fcmtoken != null) {
        // Store the token
        SharedPreferences prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', fcmtoken);

        // Get user data and update server
        userNames = prefs.getStringList('users') ?? [];
        if (userNames.isNotEmpty) {
          Auth_Users();
        }
      }

      // Set up message handlers
      _setupMessageHandlers();
    }
  }

  // Handle messages when app is opened from terminated state
  void handleInitialMessage(RemoteMessage? message) {
    if (message != null) {
      redirectToNotificationScreen(message);
    }
  }

  // Handle messages when app is opened from background state
  void handleMessage(RemoteMessage message) async {
    if (message == null) {
      return;
    } else {
      _handleNotificationDisplay(message);
    }
  }

  // Process and display the notification
  void _handleNotificationDisplay(RemoteMessage message) async {
    redirectToNotificationScreen(message);
  }

  // Redirect to appropriate screen based on notification type
  void redirectToNotificationScreen(RemoteMessage message) async {
    if (message == null) {
      return;
    } else {
      if (message != null &&
          message.data != null &&
          message.data['type'] != null &&
          message.data['type'] == '3') {
        print('broadcast if statement');

        print(userNames[0]);

        List<dynamic> body = jsonDecode(message.data['body']);

        if (body.isNotEmpty) {
          print(body[0]['class_name']);

          if (userNames.length > 0) {
            Get.off(
              MainPageChat(
                staff_id: body[0]['id'].toString(),
                student_id: userNames[0],
                Section_name: body[0]['section_name'],
                class_name: body[0]['class_name'],
                Profile: body[0]['profile'],
                name: body[0]['name'],
                sender_id: userNames[0],
                message_count: 1,
                class_id: userNames.last == '1' ? userNames[6] : userNames[6],
                section_id: userNames.last == '1' ? userNames[5] : userNames[5],
                branch_id: userNames.last == '1' ? userNames[7] : userNames[7],
              ),
            );
          }
        }
      } else if (message != null &&
          message.data != null &&
          message.data['type'] != null &&
          message.data['type'] == '5') {
        List<dynamic> body = jsonDecode(message.data['body']);
        if (body.isNotEmpty) {
          var dos = body[0]['given_date'];
          Get.to(
            DairySecondScreenDetails(
              user_id: body[0]['id'],
              title: body[0]['title'],
              description: body[0]['description'],
              posted_date: body[0]['date'],
              ending_date: dos,
              url: body[0]['url'] != null ? body[0]['url'] : '',
              teacher_name:
                  body[0]['role'] == 2 ? body[0]['staff_name'] : 'Admin',
            ),
          );
        }
      } else if (message != null &&
          message.data != null &&
          message.data['type'] != null &&
          message.data['type'].toString() == '6') {
        Get.to(Attendance());
      } else if (message != null &&
          message.data != null &&
          message.data['type'] != null &&
          message.data['type'].toString() == '1') {
        Get.to(
          panel.NotificationSecondScreenDetails(
            title: message.data['title'] ?? 'Network issues',
            description: message.data['body'] ?? '',
            url: message.data['url'],
            user_id: '0',
          ),
        );
      } else {
        print('ggggggfgfgg');
        // fetchstudent(message.data['id']);

        Get.to(
          panel.NotificationSecondScreenDetails(
            title: message.data['title'] ?? 'Network issues',
            description: message.data['body'] ?? '',
            url: message.data['url'],
            user_id: '0',
          ),
        );
      }
    }
  }

  // Fetch student data
  fetchstudent(id) async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;

    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth-account';
    final data = {'id': id};

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        result['data'],
      );

      List<dynamic> dataList = result['data'];

      List<dynamic> filteredData =
          dataList.where((item) {
            if ((int.parse(item['academic_year'].toString().split('-')[0]) ==
                    currentYear) &&
                month >= 5) {
              return true;
            } else if ((int.parse(
                      item['academic_year'].toString().split('-')[1],
                    ) ==
                    currentYear) &&
                month <= 4) {
              return true;
            } else {
              return false;
            }
          }).toList();

      print(filteredData);

      if (filteredData.length > 0) {
        filteredData
            .map(
              (result) => setListString(
                id: result['id'].toString(),
                name: result['name'],
                class_name: result['class_name'],
                branch_name: result['branch_name'],
                section_name: result['section_name'],
                section_id: result['section_id'].toString(),
                class_id: result['class_id'].toString(),
                branch_id: result['branch_id'].toString(),
                type: result['type'].toString(),
                profile: result['profile'],
                bloodgroup: result['blood_group_name'],
                fathername: result['father_name'],
                mothername: result['mother_name'],
                primarynumber: result['primary_number'].toString(),
                secondarynumber: result['secondary_number'].toString(),
                year_id: result['academic_id'].toString(),
                status_type: '1',
              ),
            )
            .toList();
      }
    } else {
      print('tttttttttttttttttttttelse');
      throw Exception('Failed to load data');
    }
  }

  // Save user data to SharedPreferences
  Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
    required String year_id,
    required String status_type,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove('users');

    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      year_id,
      type,
    ]);
  }
}
