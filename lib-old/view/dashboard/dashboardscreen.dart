// ignore_for_file: avoid_unnecessary_containers, sized_box_for_whitespace

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/User_Profile_Screen/profileScreen.dart';
import 'package:silverleaf/view/categoryListScreen/about_school.dart';
import 'package:silverleaf/view/categoryListScreen/calendae_of_events.dart';
import 'package:silverleaf/view/categoryListScreen/event_gallery.dart';
import 'package:silverleaf/view/categoryListScreen/fees_info.dart';
import 'package:silverleaf/view/categoryListScreen/attendance.dart';
// import 'package:silverleaf/view/categoryListScreen/kid's_attendance.dart';
import 'package:silverleaf/view/categoryListScreen/leaveRequest.dart';
import 'package:silverleaf/view/categoryListScreen/notification_panel.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/gallery/gallerymainscreen.dart';
import 'package:silverleaf/view/message/messagescreen.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:silverleaf/view/categoryListScreen/leavedetails.dart';

class DashBoardScreen extends StatefulWidget {
  const DashBoardScreen({super.key});

  @override
  State<DashBoardScreen> createState() => _DashBoardScreenState();
}

class _DashBoardScreenState extends State<DashBoardScreen> {
  List userNames = [];

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
          print('student');
        } else if (userNames.last == '2') {
          print('teacher');
          setState(() {
            isTeacher = true;
          });
        } else {
          print('failed');
        }
      });
    }
  }

  OverlayEntry? _overlayEntry;
  void initState() {
    _overlayEntry?.remove();
    super.initState();
    getListString();

    // final userList = getListString();
    // print('User List: $userList');
    //print(getListString());
    // getListString();
  }

  // void dispose() {
  //   print('Dashboard used');
  //   super.dispose();
  // }

  // void deactivate() {
  //   print('didactivate called');
  //   super.deactivate();
  // }
  void didChangeDependencies() {
    super.didChangeDependencies();
    //getListString();

    print('didUpdateWidget called');
  }

  void reloadState() {
    setState(() {
      // Resetting the counter for example
      print('test dashboard');
    });
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.9,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              // Your content for the draggable scrollable sheet
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    // Add your content here
                    Text('Your Draggable Scrollable Sheet Content'),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Center(
        child: Column(
          children: [
            // SizedBox(
            //   height: 8.0.hp,
            // ),
            ProfileSection(),
            SizedBox(height: 3.0.hp),
            Birthday(),
            SizedBox(height: 3.0.hp),
            GridSection(),
            // ElevatedButton(
            //   onPressed: _showBottomSheet,
            //   child: Text('Show Draggable Scrollable Sheet'),
            // ),
          ],
        ),
      ),
    );
  }
}

class GridSection extends StatefulWidget {
  const GridSection({super.key});

  @override
  State<GridSection> createState() => _GridSectionState();
}

class _GridSectionState extends State<GridSection> {
  List userNames = [];
  List itemTitleStudent = [
    'School Diary',
    "Leave Request ",
    "Calendar of Events",
    "Event Gallery",
    "Kid’s Attendance",
    'Fees \nInfo',
    'Message Dock',
    'About School',
    'Notification panel',
  ];

  List itemIconStudent = [
    "images/School Dairy.png",
    'images/Group 151.png',
    'images/Group 121.png',
    'images/event gallery.png',
    'images/Vector 29.png',
    'images/Vectorfees.png',
    'images/message dock.png',
    'images/about.png',
    'images/Group.png',
  ];

  List schoolcategoryScreens = [
    const DairyMainScreen(),
    // const LeaveDetails(),
    const LeaveRequest(),
    const CalenderOfEvents(),
    const GalleryMainScreen(),
    // const EventGallery(),
    const Attendance(),
    const FeesInfo(),

    const MessageMainScreen(),
    const AboutSchool(),

    const NotificationPanel(),
  ];

  var categoryindex = 0;
  //teacher catlogs
  List itemTitleteacher = [
    'School Diary',
    "Leave Request",
    "Calendar of Events",
    "Event Gallery",
    "Take Attendance",
    'About School',
    'Notification panel',
  ];
  List itemIconteacher = [
    "images/Vector.png",
    'images/Group 151.png',
    'images/Group 121.png',
    'images/Vectorgal.png',
    'images/Vector 29.png',
    'images/about.png',
    'images/Group.png',
  ];
  List teachercategoryScreens = [
    const SchoolDairy(),
    const LeaveRequest(),
    const CalenderOfEvents(),
    const EventGallery(),
    const Attendance(),
    const AboutSchool(),
    const NotificationPanel(),
  ];

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      //  print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        } else {}
      });
    }
  }

  void initState() {
    super.initState();
    // getListString();

    // final userList = getListString();
    // print('User List: $userList');
    //print(getListString());
    // getListString();
  }

  void didChangeDependencies() {
    super.didChangeDependencies();
    getListString();

    print('didUpdateWidget called dashboard');
  }

  void dispose() {
    print('dashboard closed');
    super.dispose();
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.9,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              // Your content for the draggable scrollable sheet
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    // Add your content here
                    Text('Your Draggable Scrollable Sheet Content'),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        child: GridView.builder(
          padding: EdgeInsets.all(20.0.sp),
          itemCount:
              isTeacher == false
                  ? itemTitleStudent.length
                  : itemTitleteacher.length,
          shrinkWrap: true,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 3,
            mainAxisSpacing: 1.0.sp,
          ),
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  isTeacher == false
                      ? Get.to(schoolcategoryScreens[index])
                      : Get.to(teachercategoryScreens[index]);
                });
              },
              onLongPress: () {
                if (index == 1) {
                  setState(() {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => LeaveDetails()),
                    );
                  });
                  // Navigator.push(context,
                  //     MaterialPageRoute(builder: (context) => LeaveDetails()));
                }
                // Navigator.push(context,
                //     MaterialPageRoute(builder: (context) => LeaveDetails()));
                // Handle long press action here
                print(itemTitleStudent[index]);
              },
              child: SizedBox(
                child: Column(
                  children: [
                    Container(
                      height: 5.0.hp,
                      width: 15.0.wp,
                      // color: Colors.blueAccent,
                      child: Image.asset(
                        isTeacher == true
                            ? itemIconteacher[index].toString()
                            : itemIconStudent[index].toString(),
                      ),
                    ),
                    SizedBox(height: 1.0.hp),
                    Expanded(
                      child: SizedBox(
                        width: 22.0.wp,
                        child: Text(
                          isTeacher == false
                              ? itemTitleStudent[index]
                              : itemTitleteacher[index],
                          style: GoogleFonts.poppins(
                            color: Colors.black,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class Birthday extends StatefulWidget {
  const Birthday({super.key});

  @override
  State<Birthday> createState() => _BirthdayState();
}

class _BirthdayState extends State<Birthday> {
  var products = [];

  List userNames = [];

  Future<List> fetchApiData() async {
    print(userNames[14]);
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/students/student-birthday/${userNames.last}/${userNames[14]}',
      ),
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print('api response data');

      List<dynamic> jsonData = responseBody['data'];
      products.clear();
      setState(() {
        jsonData.forEach((result) {
          if (result['type'] == 1) {
            products.add({
              'name': result['name'],
              'class_name': result['class_name'],
              'section_name': result['section_name'],
              'profile': result['profile'],
              'type': 1,
            });
          } else {
            products.add({
              'name': result['name'],
              'profile': result['profile'],
              'type': 2,
            });
          }
        });
      });

      //print(this.jsonData);
      return jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchApiData();
      });
    }
  }

  void initState() {
    super.initState();
    getListString();
  }

  Widget build(BuildContext context) {
    if (products.length > 0) {
      return Container(
        height: 20.0.hp,
        color: Color(0xFFE6F1E0),
        child: Column(
          children: [
            SizedBox(height: 1.0.hp),
            Text(
              "Today's Birthdays",
              style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 2.0.hp),
            CarouselSlider(
              options: CarouselOptions(
                height: 100.0,
                enlargeCenterPage: true,
                autoPlay: true,
                aspectRatio: 16 / 9,
                autoPlayCurve: Curves.fastOutSlowIn,
                enableInfiniteScroll: true,
                autoPlayAnimationDuration: Duration(milliseconds: 800),
                viewportFraction: 0.8,
              ),
              items:
                  products.map((product) {
                    return Builder(
                      builder: (BuildContext context) {
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          margin: EdgeInsets.symmetric(horizontal: 5.0),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(
                              color:
                                  Colors
                                      .white, // You can set the color of the border here
                              width:
                                  1.0, // You can set the width of the border here
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 1,
                                child: Padding(
                                  padding: EdgeInsets.only(
                                    top: 8.0,
                                    bottom: 8.0,
                                    left: 25.0,
                                  ), // Adjust top and bottom padding as needed
                                  child: CircleAvatar(
                                    radius: 40.0.sp,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(80.0),
                                      child: Image.network(
                                        product['profile'],
                                        width: double.infinity,
                                        height: double.infinity,
                                        fit: BoxFit.cover,
                                        errorBuilder: (
                                          context,
                                          error,
                                          stackTrace,
                                        ) {
                                          return Image.asset(
                                            'images/user.png',
                                            width: double.infinity,
                                            height: double.infinity,
                                          );
                                        },
                                      ),
                                    ),
                                  ),

                                  // Image.network(
                                  //   product['profile'],
                                  //   //  fit: BoxFit.cover,
                                  //   width: double.infinity,
                                  //   height: double.infinity,
                                  // ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 20.0,
                                  ),
                                  child: Text(
                                    product['type'] == 1
                                        ? '${product['name']}\nstd-${product['class_name']},${product['section_name']}'
                                        : '${product['name']}\nTeacher',
                                    style: TextStyle(fontSize: 18.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  }).toList(),
            ),
          ],
        ),
      );
    } else {
      return Container();
    }
  }
}

class ProfileSection extends StatefulWidget {
  const ProfileSection({super.key});

  @override
  State<ProfileSection> createState() => _ProfileSectionState();
}

class _ProfileSectionState extends State<ProfileSection> {
  List userNames = [];
  save_button() {
    print('save button clicked');
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          print('student');
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
          print('teacher');
        } else {
          print('failed');
        }
      });
    }
  }

  void initState() {
    super.initState();

    getListString();
  }

  void dispose() {
    print('dispose');
    // if (_isDropdownOpen) {
    //   _overlayEntry?.remove();
    // }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return userNames.isEmpty
        ? Center(child: CircularProgressIndicator())
        : GestureDetector(
          onTap: () {
            Get.to(
              ProfileScreen(
                student_profile: userNames[8],
                student_name: userNames[1],
                student_class: userNames[2],
                student_section: userNames[4],
                student_branch: userNames[3],
                student_blood_group: userNames[9],
                student_father_name: userNames[10],
                student_mother_name: userNames[11],
                student_primary_contact_no: userNames[12],
                student_secondary_contact_no: userNames[13],
                id: userNames[0],
              ),
            );
          },
          child: Container(
            // color: Colors.amber,
            height: 20.0.hp,
            alignment: Alignment.center,
            width: MediaQuery.of(context).size.width,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // CircleAvatar(
                //   radius: 40.0.sp,
                //   child: ClipRRect(
                //     borderRadius: BorderRadius.circular(80.0),
                //     child: Image.network(
                //       userNames[8],
                //       width: 160,
                //       height: 160,
                //       fit: BoxFit.cover,
                //       errorBuilder: (context, error, stackTrace) {
                //         return Image.asset(
                //           'images/user.png',
                //           width: 160,
                //           height: 160,
                //         );
                //       },
                //     ),
                //   ),
                // ),
                CircleAvatar(
                  radius: 40.0.sp,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(80.0),
                    child: Image.network(
                      userNames[8],
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          'images/user.png',
                          width: double.infinity,
                          height: double.infinity,
                        );
                      },
                    ),
                  ),
                ),

                SizedBox(
                  width: 20, // Adjust the percentage as needed
                ),

                // Text(
                //   isTeacher == true ? userNames[1] : userNames[1],
                //   style: textStyle,
                // ),
                SingleChildScrollView(
                  child: Text(
                    '${userNames[1]}\nstd-${userNames[2]},${userNames[4]}\n${userNames[3]}',
                    style: TextStyle(fontSize: 18.0),
                  ),
                ),

                // Text(
                //   isTeacher == true
                //       ? "Std -  ${userNames[2]}, ${userNames[4]}"
                //       : 'Std - ${userNames[2]}, ${userNames[4]} - ${userNames[3]}',
                //   style: textStyle.copyWith(fontSize: 18),
                // ),
                SizedBox(height: 5.0.hp),
              ],
            ),
          ),
        );
  }
}
