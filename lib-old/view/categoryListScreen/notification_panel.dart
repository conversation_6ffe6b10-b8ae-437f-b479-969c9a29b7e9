// ignore_for_file: avoid_unnecessary_containers, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:url_launcher/url_launcher.dart';

class NotificationPanel extends StatefulWidget {
  const NotificationPanel({super.key});

  @override
  State<NotificationPanel> createState() => _NotificationPanelState();
}

class _NotificationPanelState extends State<NotificationPanel> {
  var jsonData;
  List userNames = [];
  Future<List> fetchnotificationData() async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/notification/view-notification/${userNames[6]}/${userNames[7]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];
      this.jsonData = responseBody['data'];
      print(this.jsonData);
      return this.jsonData;
    } else {
      return [];
      // throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchnotificationData();
      });
    }
  }

  void initState() {
    this.getListString();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(MediaQuery.of(context).size.width, 9.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'Notification panel',
            screen_id: 2,
          ),
        ),
        body: Container(
          child: FutureBuilder<List<dynamic>>(
            future: fetchnotificationData(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                print('if-dta-block');
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                print('error');
                return Center(child: CircularProgressIndicator());
              } else {
                List<dynamic> data = snapshot.data!;
                print(data.length);
                if (snapshot.data!.length == 0) {
                  return Center(child: Text('No Data'));
                } else {
                  return ListView.builder(
                    padding: EdgeInsets.all(20.0.sp),
                    itemCount: snapshot.data!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final item = data[index];

                      // Parse the input date string into a DateTime object
                      DateTime inputDate = DateTime.parse(item['created_at']);

                      // Format the DateTime object into "MMM dd yyyy" format
                      String formattedDate = DateFormat(
                        'MMM dd yyyy',
                      ).format(inputDate);
                      return GestureDetector(
                        onTap: () {
                          print('click');
                        },
                        child: Container(
                          color:
                              item["announcement_type"].toString() == "5"
                                  ? Color(0xffF4FFEE)
                                  : Colors.transparent,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 12.0,
                            ), // Add padding to left and right
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CircleAvatar(
                                      backgroundImage: ExactAssetImage(
                                        'images/user.png',
                                      ),
                                    ),
                                    SizedBox(width: 3.0.wp),
                                    Center(
                                      child: Text(
                                        item["annocement_name"].toString(),
                                        style: textStyle.copyWith(
                                          fontWeight: FontWeight.w800,
                                          fontSize: 12.0.sp,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (item["annocement_name"]
                                        .toString()
                                        .toLowerCase() ==
                                    'leave') ...[
                                  SizedBox(height: 2.0.hp),
                                  Text(
                                    maxLines: 5, // Limit the text to two lines
                                    overflow: TextOverflow.ellipsis,
                                    '${item['date'].toString()} to ${item['to_date'].toString()}',
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                                SizedBox(height: 2.0.hp),
                                Text(
                                  maxLines: 5, // Limit the text to two lines
                                  overflow: TextOverflow.ellipsis,
                                  item['description'].toString(),
                                  textAlign: TextAlign.left,
                                  style: textStyle.copyWith(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 10.0.sp,
                                  ),
                                ),
                                SizedBox(height: 2.hp),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Align(
                                        alignment: Alignment.bottomLeft,
                                        child: GestureDetector(
                                          onTap: () {
                                            // Handle "Posted Date" action
                                          },
                                          child: Text(
                                            'Posted - ${formattedDate}',
                                            style: TextStyle(
                                              color: Colors.grey[700],
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Align(
                                        alignment: Alignment.bottomRight,
                                        child: GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder:
                                                    (
                                                      context,
                                                    ) => NotificationSecondScreenDetails(
                                                      title:
                                                          item['annocement_name'],
                                                      description:
                                                          item['description'],
                                                      url:
                                                          item['url'] != null
                                                              ? item['url']
                                                              : '',
                                                      user_id: '0',
                                                    ),
                                              ),
                                            );
                                          },
                                          child: Text(
                                            'Read More>',
                                            style: TextStyle(
                                              color: appcolor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Divider(),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }
              }
            },
          ),
        ),
      ),
    );
  }
}

class NotificationSecondScreenDetails extends StatefulWidget {
  final String title;
  final String description;

  final String user_id;

  final String url;

  const NotificationSecondScreenDetails({
    super.key,
    required this.title,
    required this.description,
    required this.url,
    required this.user_id,
  });

  @override
  State<NotificationSecondScreenDetails> createState() => NotificationDetails();
}

class NotificationDetails extends State<NotificationSecondScreenDetails> {
  subback() {
    if (widget.user_id == '0') {
      Navigator.pop(context, true);
    } else {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => MainBoard()),
      );
    }

    // Get.to(const MainBoard());

    print('back button widget clicked diary');
    //Get.forceAppUpdate();
  }

  void initState() {
    if (widget.user_id != '0') {
      fetchstudent(widget.user_id);
    }
    super.initState();
  }

  fetchstudent(id) async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;

    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth-account';
    final data = {'id': id};

    final response = await http.post(Uri.parse(url), body: data);
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        result['data'],
      );

      List<dynamic> dataList = result['data'];

      List<dynamic> filteredData =
          dataList.where((item) {
            if ((int.parse(item['academic_year'].toString().split('-')[0]) ==
                    currentYear) &&
                month >= 5) {
              return true;
            } else if ((int.parse(
                      item['academic_year'].toString().split('-')[1],
                    ) ==
                    currentYear) &&
                month <= 4) {
              return true;
            } else {
              return false;
            }
          }).toList();

      print(filteredData);

      if (filteredData.length > 0) {
        filteredData
            .map(
              (result) => setListString(
                id: result['id'].toString(),
                name: result['name'],
                class_name: result['class_name'],
                branch_name: result['branch_name'],
                section_name: result['section_name'],
                section_id: result['section_id'].toString(),
                class_id: result['class_id'].toString(),
                branch_id: result['branch_id'].toString(),
                type: result['type'].toString(),
                profile: result['profile'],
                bloodgroup: result['blood_group_name'],
                fathername: result['father_name'],
                mothername: result['mother_name'],
                primarynumber: result['primary_number'].toString(),
                secondarynumber: result['secondary_number'].toString(),
                year_id: result['academic_id'].toString(),
                status_type: '1',
              ),
            )
            .toList();
      }
      setState(() {});
      // print(student_data_profile.length);

      //return result['data'];
    } else {
      print('tttttttttttttttttttttelse');
      throw Exception('Failed to load data');
    }
  }

  Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
    required String year_id,
    required String status_type,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove('users');

    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      year_id,
      type,
    ]);
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw 'Could not launch $url';
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: () {
              subback();
            },
            profile: () {},
            screenName: widget.title,
            screen_id: 2,
          ),
        ),
        body: WillPopScope(
          onWillPop: () => subback(),
          child: Column(
            children: [
              SizedBox(height: 2.0.hp),
              Expanded(
                child: Container(
                  child: Padding(
                    padding: EdgeInsets.all(17.0.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.description,
                          style: dairyTextStyle.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (widget.url != '') ...[
                          GestureDetector(
                            onTap: () {
                              _launchUrl(widget.url);
                            },
                            child: Text(
                              widget.url,
                              style: dairyTextStyle.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
