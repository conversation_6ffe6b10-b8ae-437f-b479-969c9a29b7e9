// ignore_for_file: avoid_print, prefer_interpolation_to_compose_strings, prefer_const_constructors

// import 'dart:developer';

import 'package:flutter/material.dart';
// import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:silverleaf/view/teachers_Screeen/leaverequestListTeacherScreen.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:silverleaf/controller/student_leave_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LeaveRequest extends StatefulWidget {
  const LeaveRequest({super.key});

  @override
  State<LeaveRequest> createState() => _LeaveRequestState();
}

class _LeaveRequestState extends State<LeaveRequest> {
  StudentLeaveController studentController = StudentLeaveController();
  TextEditingController leavestatus = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController fromdate = TextEditingController();
  TextEditingController todate = TextEditingController();

  List<String> list = <String>[
    'Select',
    'Sick',
    'Personal',
    'Family Function',
    'other',
  ];
  bool isLoading = false;
  String dropdownValue = '';
  String? selectedValue;
  final String defaultValue = '3';
  int count = 0;
  List userNames = [];
  final List<DropdownMenuItem<String>> items = [
    DropdownMenuItem(value: '1', child: Center(child: Text('Select'))),
    DropdownMenuItem(value: '2', child: Center(child: Text('Sick'))),
    DropdownMenuItem(value: '3', child: Center(child: Text('Personal'))),
    DropdownMenuItem(value: '4', child: Center(child: Text('Family Function'))),
    DropdownMenuItem(value: '5', child: Center(child: Text('Other'))),
  ];
  void initState() {
    super.initState();
    selectedValue = '1';
    getListString();
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child:
          isTeacher == true
              ? LeaveRequestListTeacherScreen()
              : Scaffold(
                resizeToAvoidBottomInset: false,
                backgroundColor: Colors.white,
                appBar: PreferredSize(
                  preferredSize: Size(double.infinity, 9.0.hp),
                  child: CustomizedAppBar(
                    back: back,
                    profile: () {},
                    screenName: 'Leave Request',
                    screen_id: 2,
                  ),
                ),
                body: Container(
                  padding: EdgeInsets.all(17.0.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 1.0.hp),
                      Text(
                        "Select Leave",
                        style: textStyle.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          fontSize: 10.0.sp,
                        ),
                      ),
                      SizedBox(height: 1.0.hp),
                      SizedBox(
                        child: DropdownButtonFormField<String>(
                          isExpanded: true,
                          value: selectedValue,
                          items: items.toList(),
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.zero,
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(7),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          onChanged: (String? value) {
                            setState(() {
                              if (value != '') {
                                selectedValue = value!;
                              } // Update the selected value.
                            });
                          },
                        ),
                      ),
                      // const DropdownMenuExample(),
                      SizedBox(height: 3.0.hp),
                      Text(
                        "Description",
                        style: textStyle.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          fontSize: 10.0.sp,
                        ),
                      ),
                      SizedBox(height: 1.0.hp),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey, width: 1),
                          borderRadius: BorderRadius.circular(10.0.sp),
                        ),
                        height:
                            200, //     <-- TextField expands to this height.
                        child: TextField(
                          controller: descriptionController,
                          maxLines: null, // Set this
                          expands: true, // and this
                          keyboardType: TextInputType.multiline,
                        ),
                      ),
                      SizedBox(height: 2.0.hp),
                      SizedBox(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Text(
                              "From",
                              style: textStyle.copyWith(
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 10.0.sp,
                              ),
                            ),
                            GestureDetector(
                              onTap: () => showMyDialog(context),
                              child: dateContainer(
                                title:
                                    showStartingDate == ''
                                        ? ""
                                        : showStartingDate,
                              ),
                            ),
                            SizedBox(width: 2.0.wp),
                            SizedBox(
                              height: 5.0.hp,
                              child: const VerticalDivider(
                                width: 2,
                                thickness: 2,
                                color: Colors.black,
                                endIndent: 6,
                                indent: 6,
                              ),
                            ),
                            Text(
                              "To",
                              style: textStyle.copyWith(
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 10.0.sp,
                              ),
                            ),
                            SizedBox(width: 3.0.wp),
                            GestureDetector(
                              onTap: () => showEndDateMyDialog(context),
                              child: dateContainer(
                                title: showEndDate == '' ? '' : showEndDate,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 2.0.hp),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            if (!isLoading) {
                              isLoading = true;
                              Future.delayed(Duration.zero, () async {
                                await leaveRequestPopUp(context);
                              }).then((value) {
                                print('leave request');
                                print(value);

                                Get.back();
                              });
                            }
                          });
                        },
                        child: Container(
                          height: 6.0.hp,
                          width: MediaQuery.of(context).size.width - 20,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: appcolor,
                            borderRadius: BorderRadius.circular(7.0.sp),
                          ),
                          child:
                              !isLoading
                                  ? Text(
                                    "Request",
                                    style: textStyle.copyWith(
                                      color: Colors.white,
                                      fontSize: 12.0.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  )
                                  : Center(
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                    ),
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  String _selectedDate = '';
  DateTime? selectedDateandYear;
  String _selectedEndDate = '';
  String _dateCount = '';
  String _range = '';
  String showStartingDate = '';
  String showEndDate = '';
  String endrange = '';
  String _rangeCount = '';
  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      // if (args.value is PickerDateRange) {
      //   _selectedDate = args.value.toString();
      //   selectedDateandYear = args.value;
      //   _selectedDate = DateFormat('dd/MM/yyyy').format(args.value);
      //   _range = '${DateFormat('dd/MM/yyyy').format(args.value.startDate)} -'
      //       // ignore: lines_longer_than_80_chars
      //       ' ${DateFormat('dd/MM/yyyy').format(args.value.endDate ?? args.value.startDate)}';
      //   print('if-date-picker');
      //   print("Range" + _range);
      // } else if (args.value is DateTime) {
      //   _selectedDate = args.value.toString();
      //   selectedDateandYear = args.value;
      //   _selectedDate = DateFormat('dd/MM/yyyy').format(args.value);
      //   print('else-if-date-picker');
      //   print("Select$_selectedDate");
      // } else if (args.value is List<DateTime>) {
      //   _dateCount = args.value.toString();
      //   print('else-if-date-pickers1');
      //   print("Count" + _dateCount);
      // } else {
      //   _rangeCount = args.value.length.toString();
      //   print('else picker');
      //   print("RangeCount" + _rangeCount);
      // }

      if (args.value is PickerDateRange) {
      } else if (args.value is DateTime) {
        _selectedDate = args.value.toString();

        _selectedDate = DateFormat('dd/MM/yyyy').format(args.value);
        print("Select" + _selectedEndDate);
      } else if (args.value is List<DateTime>) {
      } else {}
    });
  }

  void _onSelectionEndDateChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      if (args.value is PickerDateRange) {
      } else if (args.value is DateTime) {
        _selectedEndDate = args.value.toString();

        _selectedEndDate = DateFormat('dd/MM/yyyy').format(args.value);
        print("Select" + _selectedEndDate);
      } else if (args.value is List<DateTime>) {
      } else {}
    });
  }

  Widget dateContainer({title}) {
    return Container(
      height: 4.0.hp,
      width: 25.0.wp,
      decoration: BoxDecoration(
        border: Border.all(color: appcolor, width: .5.wp),
        borderRadius: BorderRadius.circular(5.0.sp),
      ),
      alignment: Alignment.center,
      child: Text(
        title,
        style: textStyle.copyWith(color: Colors.black, fontSize: 10.0.sp),
      ),
    );
  }

  final DateRangePickerController fromDateController =
      DateRangePickerController();
  final DateRangePickerController toDateController =
      DateRangePickerController();
  showMyDialog(context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            height: 50.0.hp,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SfDateRangePicker(
                    view: DateRangePickerView.month,
                    selectionMode: DateRangePickerSelectionMode.single,
                    initialSelectedDate: selectedDateandYear,
                    showNavigationArrow: true,
                    controller: fromDateController,
                    onSelectionChanged: _onSelectionChanged,
                  ),
                  TextButton(
                    child: const Text('Select'),
                    onPressed: () {
                      print('clicked');
                      setState(() {
                        showStartingDate = _selectedDate;
                        Navigator.of(context).pop();
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  leaveRequestPopUp(context) async {
    this.leavestatus.text = selectedValue!;

    // print(this.leavestatus.text);

    this.fromdate.text = this._selectedEndDate;
    this.todate.text = this._selectedDate;
    final Map<String, dynamic> jsonData = await this.studentController
        .savestudent(
          this.leavestatus.text,
          this.descriptionController.text,
          this.fromdate.text,
          this.todate.text,
          userNames[0],
          userNames[14],
          userNames[6],
          userNames[7],
          userNames[5],
        );
    print(jsonData['status']);
    if (jsonData['status'].toString() == 'saved') {
      isLoading = false;

      return showDialog(
        context: context,
        useSafeArea: true,
        builder: (BuildContext context) {
          return Dialog(
            child: Container(
              height: 20.0.hp,
              alignment: Alignment.center,
              child: const Text(
                "Your Leave have been \nRequested Successfully ",
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      );
    } else {
      isLoading = false;

      return showDialog(
        context: context,
        useSafeArea: true,
        builder: (BuildContext context) {
          return Dialog(
            child: Container(
              height: 20.0.hp,
              alignment: Alignment.center,
              child: const Text(
                "Your Leave have been \nRequested Failed ",
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      );
    }
  }

  showEndDateMyDialog(context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            height: 50.0.hp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SfDateRangePicker(
                  view: DateRangePickerView.month,
                  showNavigationArrow: true,
                  selectionMode: DateRangePickerSelectionMode.single,
                  initialDisplayDate: selectedDateandYear,
                  controller: toDateController,
                  onSelectionChanged: _onSelectionEndDateChanged,
                ),
                TextButton(
                  child: const Text('Select'),
                  onPressed: () {
                    setState(() {
                      showEndDate = _selectedEndDate;
                      Navigator.of(context).pop();
                    });
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

List<String> list = <String>[
  'Select',
  'Sick',
  'Personal',
  'Family Function',
  'other',
];

class DropdownMenuExample extends StatefulWidget {
  const DropdownMenuExample({super.key});

  @override
  State<DropdownMenuExample> createState() => _DropdownMenuExampleState();
}

class _DropdownMenuExampleState extends State<DropdownMenuExample> {
  String dropdownValue = list.first;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: DropdownMenu<String>(
        width: MediaQuery.of(context).size.width - 10.0.wp,
        initialSelection: list.first,
        onSelected: (String? value) {
          // This is called when the user selects an item.
          setState(() {
            dropdownValue = value!;
          });
        },
        dropdownMenuEntries:
            list.map<DropdownMenuEntry<String>>((String value) {
              return DropdownMenuEntry<String>(value: value, label: value);
            }).toList(),
      ),
    );
  }
}

class DatePickerWidgets extends StatefulWidget {
  const DatePickerWidgets({super.key});

  @override
  State<DatePickerWidgets> createState() => _DatePickerWidgetsState();
}

class _DatePickerWidgetsState extends State<DatePickerWidgets> {
  String _selectedDate = '';
  String _dateCount = '';
  String _range = '';
  String _rangeCount = '';
  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      if (args.value is PickerDateRange) {
        _range =
            '${DateFormat('dd/MM/yyyy').format(args.value.startDate)} -'
            // ignore: lines_longer_than_80_chars
            ' ${DateFormat('dd/MM/yyyy').format(args.value.endDate ?? args.value.startDate)}';
      } else if (args.value is DateTime) {
        _selectedDate = args.value.toString();
      } else if (args.value is List<DateTime>) {
        _dateCount = args.value.length.toString();
      } else {
        _rangeCount = args.value.length.toString();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Stack(
        children: <Widget>[
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            height: 80,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text('Selected date: $_selectedDate'),
                Text('Selected date count: $_dateCount'),
                Text('Selected range: $_range'),
                Text('Selected ranges count: $_rangeCount'),
              ],
            ),
          ),
          Positioned(
            left: 0,
            top: 80,
            right: 0,
            bottom: 0,
            child: SfDateRangePicker(
              onSelectionChanged: _onSelectionChanged,
              selectionMode: DateRangePickerSelectionMode.range,
              initialSelectedRange: PickerDateRange(
                DateTime.now().subtract(const Duration(days: 4)),
                DateTime.now().add(const Duration(days: 3)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
