import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/controller/sample.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';

class EventGallery extends StatefulWidget {
  const EventGallery({super.key});

  @override
  State<EventGallery> createState() => _EventGalleryState();
}

class _EventGalleryState extends State<EventGallery> {
  // SampleController sampleController = Get.put(SampleController());
  @override
  void initState() {
    // fetchdata();
    // TODO: implement initState
    super.initState();
  }

  // Future fetchdata() async {
  //   // await sampleController.fetchData();
  // }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'Event Gallery',
            screen_id: 2,
          ),
        ),
      ),
    );
  }
}
