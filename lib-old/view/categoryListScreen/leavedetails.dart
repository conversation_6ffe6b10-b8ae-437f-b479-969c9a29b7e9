// ignore_for_file: avoid_unnecessary_containers, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';

class LeaveDetails extends StatefulWidget {
  const LeaveDetails({super.key});

  @override
  State<LeaveDetails> createState() => _LeaveDetailsState();
}

class _LeaveDetailsState extends State<LeaveDetails> {
  var jsonData;
  List userNames = [];
  Future<List> fetchnotificationData() async {
    final url =
        'https://silverleafms.in/silvar_leaf/api/leave/view-leave-details/${userNames[0]}/${userNames[14]}';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final responseBody = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];
      this.jsonData = responseBody['data'];
      print(this.jsonData);
      return this.jsonData;
    } else {
      return [];
      // throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchnotificationData();
      });
    }
  }

  void initState() {
    this.getListString();

    super.initState();
  }

  String formatDateString(
    String dateString,
    String inputFormat,
    String outputFormat,
  ) {
    DateTime parsedDate = DateFormat(inputFormat).parse(dateString);
    return DateFormat(outputFormat).format(parsedDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(MediaQuery.of(context).size.width, 9.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'Leave Details',
            screen_id: 2,
          ),
        ),
        body: Container(
          child: FutureBuilder<List<dynamic>>(
            future: fetchnotificationData(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                print('if-dta-block');
                return Center(child: CircularProgressIndicator());
              } else if (snapshot.hasError) {
                print('error');
                return Center(child: CircularProgressIndicator());
              } else {
                print('else');
                List<dynamic> data = snapshot.data!;
                print(data.length);
                if (snapshot.data!.length == 0) {
                  return Center(child: Text('No Data'));
                } else {
                  return ListView.builder(
                    padding: EdgeInsets.all(20.0.sp),
                    itemCount: snapshot.data!.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final item = data[index];

                      String formattedDateStr = formatDateString(
                        item['posted_date'],
                        'dd/MM/yyyy',
                        'MMMM d, y',
                      );
                      DateTime dateTime;
                      //  String formattedDate;

                      // DateTime inputDate = DateTime.parse(item['created_at']);
                      // String time = DateFormat('hh:mm a').format(inputDate);

                      // String formattedDate =
                      //     DateFormat('MMM dd yyyy').format(inputDate);

                      if (item['created_at'].toString().contains("AM") ||
                          item['created_at'].toString().contains("PM")) {
                        DateFormat inputFormat = DateFormat(
                          'yyyy-MM-dd hh:mm a',
                        );
                        dateTime = inputFormat.parse(
                          item['created_at'].toString(),
                        );
                      } else {
                        DateFormat inputFormat = DateFormat(
                          'yyyy-MM-dd HH:mm:ss',
                        );
                        dateTime = inputFormat.parse(
                          item['created_at'].toString(),
                        );
                      }

                      String formattedDate = DateFormat(
                        'd MMM y | EEEE',
                      ).format(dateTime);
                      String formattedTime = DateFormat(
                        'hh:mm a',
                      ).format(dateTime);

                      DateTime fromDate = DateFormat(
                        'dd/MM/yyyy',
                      ).parse(item['from_date']);
                      DateTime toDate = DateFormat(
                        'dd/MM/yyyy',
                      ).parse(item['to_date']);

                      String fromdates = DateFormat(
                        'MMM dd yyyy',
                      ).format(fromDate);

                      String todates = DateFormat('MMM dd yyyy').format(toDate);

                      var title;

                      var status;
                      Color statusColor;

                      if (item['leave_type'].toString() == '2') {
                        title = 'Sick';
                      } else if (item['leave_type'].toString() == '3') {
                        title = 'Personal';
                      } else if (item['leave_type'].toString() == '4') {
                        title = 'Family Function';
                      } else if (item['leave_type'].toString() == '5') {
                        title = 'Other';
                      }

                      if (item['status'].toString() == '2') {
                        status = 'Approved';
                        statusColor = appcolor;
                      } else {
                        status = 'Yet to Approve';
                        statusColor = Colors.orange;
                      }

                      return GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => LeaveSecondScreenDetails(
                                    title: title,
                                    description: item['description'],
                                    from: fromdates,
                                    to: todates,
                                    created: formattedDateStr,
                                    status: status,
                                    statusColor: statusColor,
                                  ),
                            ),
                          );
                        },
                        child: Container(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomLeft,
                                      child: GestureDetector(
                                        onTap: () {
                                          // Handle "Posted Date" action
                                        },
                                        child: Text(
                                          title,
                                          style: TextStyle(
                                            color: Colors.black,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomRight,
                                      child: GestureDetector(
                                        onTap: () {
                                          // Handle "Posted Date" action
                                        },
                                        child: Text(
                                          formattedDate + " | " + formattedTime,
                                          style: TextStyle(
                                            color: Colors.grey[700],
                                            // fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2.0.hp),
                              Text(
                                maxLines: 5, // Limit the text to two lines
                                overflow: TextOverflow.ellipsis,
                                item['description'].toString(),
                                style: textStyle.copyWith(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 10.0.sp,
                                ),
                              ),
                              SizedBox(height: 2.hp),
                              Row(
                                children: [
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomLeft,
                                      child: GestureDetector(
                                        onTap: () {
                                          // Handle "Posted Date" action
                                        },
                                        child: Text(
                                          '${fromdates} - ${todates}',
                                          style: TextStyle(
                                            color: Colors.grey[700],
                                            //  fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomRight,
                                      child: GestureDetector(
                                        onTap: () {
                                          // Navigator.push(
                                          //   context,
                                          //   MaterialPageRoute(
                                          //     builder: (context) =>
                                          //         NotificationSecondScreenDetails(
                                          //       title:
                                          //           item['annocement_name'],
                                          //       description:
                                          //           item['description'],
                                          //     ),
                                          //   ),
                                          // );
                                        },
                                        child: Text(
                                          status,
                                          style: TextStyle(
                                            color: statusColor,
                                            //  fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Divider(),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }
              }
            },
          ),
        ),
      ),
    );
  }
}

class LeaveSecondScreenDetails extends StatefulWidget {
  final String title;
  final String description;
  final String created;
  final String status;
  final String from;
  final String to;
  final statusColor;

  const LeaveSecondScreenDetails({
    super.key,
    required this.title,
    required this.description,
    required this.created,
    required this.status,
    required this.from,
    required this.to,
    required this.statusColor,
  });

  @override
  State<LeaveSecondScreenDetails> createState() => SecondLeaveDetails();
}

class SecondLeaveDetails extends State<LeaveSecondScreenDetails> {
  subback() {
    // Get.to(const MainBoard());
    Get.back();
    print('back button widget clicked diary');
    //Get.forceAppUpdate();
  }

  void initState() {
    print('created date');
    print(widget.created);
    super.initState();
  }

  String formatDateString(
    String dateString,
    String inputFormat,
    String outputFormat,
  ) {
    DateTime parsedDate = DateFormat(inputFormat).parse(dateString);
    return DateFormat(outputFormat).format(parsedDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: () {
              subback();
            },
            profile: () {},
            screenName: 'Leave Requests',
            screen_id: 2,
          ),
        ),
        body: WillPopScope(
          onWillPop: () => subback(),
          child: Container(
            child: Padding(
              padding: EdgeInsets.all(17.0.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomLeft,
                          child: GestureDetector(
                            onTap: () {
                              // Handle "Posted Date" action
                            },
                            child: Text(
                              widget.title,
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomRight,
                          child: GestureDetector(
                            onTap: () {
                              // Handle "Posted Date" action
                            },
                            child: Text(
                              widget.created,
                              style: TextStyle(
                                color: Colors.grey[700],
                                // fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 2.0.hp),
                  Text(
                    widget.description,
                    style: dairyTextStyle.copyWith(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(height: 4.hp),
                  Row(
                    children: [
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomLeft,
                          child: GestureDetector(
                            onTap: () {
                              // Handle "Posted Date" action
                            },
                            child: Text(
                              '${widget.from} - ${widget.to}',
                              style: TextStyle(
                                color: Colors.grey[700],
                                //  fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomRight,
                          child: GestureDetector(
                            onTap: () {
                              // Navigator.push(
                              //   context,
                              //   MaterialPageRoute(
                              //     builder: (context) =>
                              //         NotificationSecondScreenDetails(
                              //       title:
                              //           item['annocement_name'],
                              //       description:
                              //           item['description'],
                              //     ),
                              //   ),
                              // );
                            },
                            child: Text(
                              widget.status,
                              style: TextStyle(
                                color: widget.statusColor,
                                //  fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // SizedBox(
                  //   height: 2.0.hp,
                  // ),
                  // Expanded(
                  //   child: Container(
                  //     child: Padding(
                  //       padding: EdgeInsets.all(17.0.sp),
                  //       child: Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           Text(
                  //             widget.description,
                  //             style: dairyTextStyle.copyWith(
                  //                 fontWeight: FontWeight.w600),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ),
                  // )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
