import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/main.dart';
// import 'package:silverleaf/contest/extension.dart';
// import 'package:silverleaf/view/appbar/customizedappbar.dart';
// import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:silverleaf/view/teachers_Screeen/teacherDairyScreen.dart';

class SchoolDairy extends StatefulWidget {
  const SchoolDairy({super.key});

  @override
  State<SchoolDairy> createState() => _SchoolDairyState();
}

class _SchoolDairyState extends State<SchoolDairy> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child:
          isTeacher == true
              ? const TeacherDairyEntreyScreen()
              : const Scaffold(),
    );
  }
}

back() {
  Get.back();
}
