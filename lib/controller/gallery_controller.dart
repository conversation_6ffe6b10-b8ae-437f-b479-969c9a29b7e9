import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class GalleryController extends GetxController {
  fetchApiData() async {
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-details',
      ),
    );

    if (response.statusCode == 200) {
      print('if-block');
      final Map<String, dynamic> responseBody = json.decode(response.body);
      List<dynamic> data = responseBody['data'];
      print(data);
      return data;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<List> fetchgalleryData(int id) async {
    print('tt');
    final url =
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-all-images/$id';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final result = json.decode(response.body);
      // List<dynamic> result = responseBody['data'];
      // print(result['data']);
      return result['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }
}
