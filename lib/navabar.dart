import 'package:flutter/material.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';

class NavbarData {
  final toggleDropdown;
  final choiceAction;
  NavbarData({required this.toggleDropdown, required this.choiceAction});

  OverlayEntry createOverlayEntry(context) {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;
    var offset = renderBox.localToGlobal(Offset(0, size.height));
    List<String> _dropdownOptions = ['Profile', 'Logout'];

    return OverlayEntry(
      builder: (context) {
        return Positioned(
          left: offset.dx,
          top: offset.dy + 5.0,
          width: size.width,
          child: Material(
            elevation: 4.0,
            child: Column(
              children:
                  _dropdownOptions.map((String option) {
                    return ListTile(
                      title: Text(option),
                      onTap: () {
                        print('Selected: $option');
                        this.toggleDropdown();
                        this.choiceAction(option);
                      },
                    );
                  }).toList(),
            ),
          ),
        );
      },
    );
  }

  // var datas;
}
