// ignore_for_file: must_be_immutable, avoid_unnecessary_containers, prefer_const_constructors

import 'dart:ui';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/categoryListScreen/about_school.dart';
import 'package:silverleaf/view/otpScreen.dart';
import 'package:silverleaf/view/splash/splash_screen.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:silverleaf/controller/student_leave_controller.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:firebase_core/firebase_core.dart';
import 'api/firebase_api.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
//import 'package:silverleaf/view/categoryListScreen/notification_panel.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'package:in_app_update/in_app_update.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
//import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';

import 'package:intl/intl.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );
  await Firebase.initializeApp();

  final notificationManager = FirebaseApi();
  notificationManager.initNotifications();

  runApp(const MyApp());

  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.white, // status bar color
      statusBarIconBrightness: Brightness.dark, // status bar icons' color
      statusBarBrightness:
          Brightness.light, // for iOS (it needs a different setting)
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Silverleaf',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primary),
        useMaterial3: true,
        fontFamily: 'Inter',
        appBarTheme: AppBarTheme(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.textOnPrimary,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.divider),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.divider),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: AppColors.surfaceVariant,
        ),
      ),
      home: const SplashScreen(),
    );
  }
}

class DescriptionSlider extends StatefulWidget {
  const DescriptionSlider({super.key});

  @override
  State<DescriptionSlider> createState() => _DescriptionSliderState();
}

class _DescriptionSliderState extends State<DescriptionSlider> {
  List imageslider = [
    'images/img-1.jpg',
    'images/img-2.jpg',
    'images/img-3.jpg',
  ];
  void dispose() {
    print('Dispose used');
    super.dispose();
  }

  Color color = Color.fromRGBO(135, 188, 70, 1);
  late SharedPreferences prefs;
  var dotterdcurrentindex = 0;
  //flfinal CarouselController caroselcontroller = CarouselController();

  Future<bool> subback() async {
    exit(0);
  }

  @override
  Widget build(BuildContext context) {
    if (dotterdcurrentindex == 0) {
      color = Color.fromRGBO(135, 188, 70, 1);
    } else if (dotterdcurrentindex == 1) {
      color = Color(0xFF5EAE87);
    } else if (dotterdcurrentindex == 2) {
      color = Color(0xFF9BDCBC);
    }

    return WillPopScope(
      onWillPop: subback,
      child: Scaffold(
        backgroundColor: color,
        body: SafeArea(
          child: Column(
            children: [
              Container(height: 15.0.hp),
              Container(
                height: 53.0.hp,
                color: Colors.transparent,
                child: CarouselSlider(
                  //carouselController: caroselcontroller,
                  options: CarouselOptions(
                    // aspectRatio: 16 / 9,
                    // viewportFraction: 0.8,
                    initialPage: 0,
                    enableInfiniteScroll: false,
                    reverse: false,
                    autoPlay: false,
                    autoPlayInterval: const Duration(seconds: 3),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.fastOutSlowIn,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.3,
                    onPageChanged: (index, reason) {
                      setState(() {
                        dotterdcurrentindex = index;
                      });
                    },
                    height: MediaQuery.of(context).size.height,

                    // aspectRatio: 2.0,
                    scrollDirection: Axis.horizontal,
                  ),
                  items:
                      imageslider
                          .map(
                            (item) => Column(
                              children: [
                                Container(
                                  height: 300.0,
                                  width: 250.0,
                                  child: Center(
                                    child: Image.asset(item, fit: BoxFit.cover),
                                  ),
                                ),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 0 ? true : false,
                                  child: Text(
                                    "Your Partner in Education",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      fontWeight: FontWeight.w900,
                                      color: Colors.black,
                                      fontSize: 11.0.sp,
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 1 ? true : false,
                                  child: Text(
                                    "All You Need, Right at your Fingertips",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      fontWeight: FontWeight.w900,
                                      color: Colors.black,
                                      fontSize: 11.0.sp,
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 2 ? true : false,
                                  child: Text(
                                    "Simplify Your Schedule,\nmaximize your child's success ",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      fontWeight: FontWeight.w900,
                                      color: Colors.black,
                                      fontSize: 11.0.sp,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 0.7.hp),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 0 ? true : false,
                                  child: Text(
                                    "Stay informed, stay connected,and\nsupport your child's educational\n journey every step of the way",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      color: Colors.black,
                                      fontSize: 12.0.sp,
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 1 ? true : false,
                                  child: Text(
                                    "Access your child's academic calendar,communicate with teachers,track progress, and stay up-to-date with the latest school events and news",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      color: Colors.black,
                                      fontSize: 12.0.sp,
                                    ),
                                  ),
                                ),
                                Visibility(
                                  visible:
                                      dotterdcurrentindex == 2 ? true : false,
                                  child: Text(
                                    "Easily message school events,\nmeetings, and deadlines.Our app\n makes it simple to stay on top of\n your child's school life, so you can\nfocus on what matters most.",
                                    textAlign: TextAlign.center,
                                    style: textStyle.copyWith(
                                      color: Colors.black,
                                      fontSize: 12.0.sp,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                          .toList(),
                ),
              ),
              // SizedBox(
              //   height: 3.0.hp,
              // ),
              SizedBox(
                child: DotsIndicator(
                  dotsCount: imageslider.length,
                  position: dotterdcurrentindex,
                  decorator: DotsDecorator(
                    color: Colors.grey, // Inactive color
                    activeColor: Colors.white,
                  ),
                ),
              ),
              SizedBox(height: 1.0.hp),
              Container(
                child: ToggleButtons(
                  color: Colors.white,
                  textStyle: const TextStyle(fontWeight: FontWeight.bold),
                  renderBorder: true,
                  borderColor: Colors.white,
                  borderWidth: 1,
                  borderRadius: BorderRadius.circular(20),
                  selectedBorderColor: Colors.lightBlue.shade900,
                  children: const [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text('Skip', style: TextStyle(fontSize: 15)),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Text('Next', style: TextStyle(fontSize: 15)),
                    ),
                  ],
                  isSelected: [false, false],
                  onPressed: (d) {
                    if (d == 1) {
                      print(dotterdcurrentindex);
                      setState(() {
                        if (dotterdcurrentindex == 2) {
                          SharedPreferences.getInstance().then((sharedPrefs) {
                            prefs = sharedPrefs;

                            if (prefs.getStringList('users')?.isNotEmpty ==
                                true) {
                              Get.to(MainBoard());
                            } else {
                              Get.to(
                                LoginScreen(
                                  title: "Login With OTP",
                                  lableText: "Mobile Number.",
                                  buttonTitle: "Get OTP",
                                  function:
                                      () => Get.to(
                                        const OtpScreen(otp: '', details: []),
                                      ),
                                  otp: '',
                                  details: [],
                                ),
                              );
                            }
                          });
                        } else {
                          Future.delayed(Duration.zero, () async {
                            //await caroselcontroller.nextPage();
                          });
                        }
                        dotterdcurrentindex == 2
                            ? dotterdcurrentindex = 2
                            : dotterdcurrentindex = dotterdcurrentindex + 1;
                      });
                    } else {
                      SharedPreferences.getInstance().then((sharedPrefs) {
                        prefs = sharedPrefs;

                        if (prefs.getStringList('users')?.isNotEmpty == true) {
                          Get.to(MainBoard());
                        } else {
                          Get.to(
                            LoginScreen(
                              title: "Login With OTP",
                              lableText: "Mobile Number.",
                              buttonTitle: "Get OTP",
                              function:
                                  () => Get.to(
                                    const OtpScreen(otp: '', details: []),
                                  ),
                              otp: '',
                              details: [],
                            ),
                          );
                        }
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreen();
}

class _HomeScreen extends State<HomeScreen> {
  late SharedPreferences prefs;
  late Timer _timer;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  void dispose() {
    // ignore: avoid_print
    print('Dispose used');
    super.dispose();
  }

  Future<void> _delayedInit() async {
    //await Future.delayed(Duration(seconds: 3)); // Delay for two seconds
    print('delay statement');
    SharedPreferences.getInstance().then((sharedPrefs) {
      prefs = sharedPrefs;

      if (prefs.getBool('isFirstInstall') == true) {
        if (prefs.getStringList('users')?.isNotEmpty == true) {
          Get.to(MainBoard());
        } else {
          Get.to(
            LoginScreen(
              title: "Login With OTP",
              lableText: "Mobile Number.",
              buttonTitle: "Get OTP",
              function: () => Get.to(const OtpScreen(otp: '', details: [])),
              otp: '',
              details: [],
            ),
          );
        }
      } else {
        prefs.setBool('isFirstInstall', true);
        Get.to(DescriptionSlider());
      }
    });
  }

  final firebaseMessaging = FirebaseMessaging.instance;
  List userNames = [];

  @override
  void initState() {
    super.initState();
    checkForUpdate();
    _delayedInit();
  }

  Future<void> checkForUpdate() async {
    InAppUpdate.checkForUpdate()
        .then((info) {
          setState(() {
            if (info.updateAvailability == UpdateAvailability.updateAvailable) {
              print('update available');
              update();
            }
          });
        })
        .catchError((e) {
          print(e.toString());
        });
  }

  void update() async {
    await InAppUpdate.startFlexibleUpdate();
    InAppUpdate.completeFlexibleUpdate().then((_) {}).catchError((e) {
      print(e.toString());
    });
  }

  Future<bool> onWillPop() async {
    SystemNavigator.pop();
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: onWillPop,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Center(child: CircularProgressIndicator()),
      ),
    );
  }
}

class LoginScreen extends StatefulWidget {
  String title;
  String buttonTitle;
  String lableText;
  VoidCallback function;
  String otp;
  List details;

  LoginScreen({
    super.key,
    required this.title,
    required this.buttonTitle,
    required this.lableText,
    required this.function,
    required this.otp,
    required this.details,
  });

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  StudentLeaveController studentController = StudentLeaveController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String Authenticate = '';
  int type = 0;
  String selectedValue = '0';
  List userNames = [];
  var result;
  // clearSharedPreferences() async {
  //   final prefs = await SharedPreferences.getInstance();
  //   await prefs.clear();
  // }
  late SharedPreferences prefs;

  Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
    required String year_id,
    required String status_type,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    if (widget.details.length > 1) {
      print('users list attendance screen');
    }

    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      year_id,
      type,
    ]);

    final notificationManager = FirebaseApi();
    notificationManager.Auth_Users();

    Get.to(const MainBoard());
  }

  // Future multipleListString({
  //   required String id,
  //   required String name,
  //   required String class_name,
  //   required String branch_name,
  //   required String section_name,
  //   required String section_id,
  //   required String class_id,
  //   required String branch_id,
  //   required String type,
  //   required String profile,
  //   required String bloodgroup,
  //   required String fathername,
  //   required String mothername,
  //   required String primarynumber,
  //   required String secondarynumber,
  //   required String year_id,
  // }) async {
  //   final prefs = await SharedPreferences.getInstance();
  //   if (widget.details.length > 1) {
  //     print('users list attendance screen');
  //   }

  //   prefs.setStringList('users', [
  //     id,
  //     name,
  //     class_name,
  //     branch_name,
  //     section_name,
  //     section_id,
  //     class_id,
  //     branch_id,
  //     profile,
  //     bloodgroup,
  //     fathername,
  //     mothername,
  //     primarynumber,
  //     secondarynumber,
  //     year_id,
  //     type
  //   ]);

  //   Get.to(const MainBoard());
  // }

  void dispose() {
    super.dispose();
  }

  Future getListString() async {
    final prefs = await SharedPreferences.getInstance();
    if (prefs.containsKey('users') &&
        prefs.getStringList('users')?.isNotEmpty == true) {
      Get.to(const MainBoard());
    } else {}
  }

  Auth_User(String mobile) async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;
    final DateTime currentDate = DateTime.now();
    final int day = currentDate.day;
    final int year = currentDate.year;
    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth';
    final data = {'mobile': mobile};

    final response = await http.post(Uri.parse(url), body: data);

    final result = await json.decode(response.body);

    if (result['status'] != '' && result['status'].toString() == 'success') {
      textEditControl.text = '';

      setState(() {
        isLoading = false;
        List<dynamic> dataList = result['data'];
        print("api response value");
        print(dataList.length);
        // List<dynamic> filteredData =
        //     dataList.where((item) {
        //       print("map string value");
        //       print(item['name']);
        //       print(item['academic_year'].toString());
        //       String academicYearString = item['academic_year'].toString();

        //       List<String> yearParts = academicYearString.split('-');
        //       print("academy year");
        //       print(yearParts);
        //       // return true;
        //       if ((int.parse(item['academic_year'].toString().split('-')[0]) ==
        //               currentYear) &&
        //           month >= 5) {
        //         print("if block academy year");
        //         return true;
        //       } else if ((int.parse(
        //                 item['academic_year'].toString().split('-')[1],
        //               ) ==
        //               currentYear) &&
        //           month <= 4) {
        //         return true;
        //       } else {
        //         return false;
        //       }
        //     }).toList();

        List<dynamic> filteredData =
            dataList.where((item) {
              if (item['academic_year'] != null) {
                final List<String> yearParts = item['academic_year']
                    .toString()
                    .split('-');
                final int firstYear = int.parse(yearParts[0]);
                final int secondYear = int.parse(yearParts[1]);
                // return true;

                //  For January through March and April 1-9
                if (year == secondYear &&
                    (month < 4 || (month == 4 && day < 10))) {
                  return true;
                }
                // For April 10 through December
                else if (year == firstYear &&
                    (month > 4 || (month == 4 && day >= 10))) {
                  return true;
                }
              }
              return false;
            }).toList();

        if (filteredData.length == 0) {
          isLoading = false;
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text("Invalid Student")));
        } else {
          isLoading = false;
          Get.to(
            OtpScreen(otp: result['otp'].toString(), details: filteredData),
          );
        }
      });
      // }
    } else {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text("Invalid Mobile Number")));
    }
  }

  void initState() {
    super.initState();

    //FlutterAppBadger.updateBadgeCount(5);
    globalBottomBarIndex = 0;

    items_student.add(
      DropdownMenuItem(value: '0', child: Center(child: Text('Select'))),
    );

    //logout();
  }

  cancel() {
    textEditControl.text = '';
  }

  static Future logout() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    var session_expired = await preferences.clear();
  }

  List<DropdownMenuItem<String>> items_student = [];

  void otpVerification() async {
    int currentYear = DateTime.now().year;
    int month = DateTime.now().month;
    if (textEditControl.text != '') {
      if (textEditControl.text == widget.otp) {
        try {
          // await FlutterDynamicIcon.setApplicationIconBadgeNumber(5);
        } on PlatformException {
          print('exception not found platform');
        } catch (e) {
          print(e);
        }

        textEditControl.text = '';
        isTeacher = false;
        if (widget.details.length > 1) {
          List multiple_user = [];

          setState(() {
            isLoading = false;
            widget.details.map((data) {
              print('multiple-user');
              print('otp verification');

              print("otp verification more than number");

              // if ((int.parse(data['academic_year'].toString().split('-')[0]) ==
              //         currentYear) &&
              //     month >= 5) {
              //   print("if block academy year");

              //   items_student.add(
              //     DropdownMenuItem(
              //       value: data['id'].toString(),
              //       child: Center(child: Text(data['name'])),
              //     ),
              //   );

              //   // return true;
              // } else if ((int.parse(
              //           data['academic_year'].toString().split('-')[1],
              //         ) ==
              //         currentYear) &&
              //     month <= 4) {
              //   items_student.add(
              //     DropdownMenuItem(
              //       value: data['id'].toString(),
              //       child: Center(child: Text(data['name'])),
              //     ),
              //   );
              //   //return true;
              // } else {
              //   return false;
              // }

              items_student.add(
                DropdownMenuItem(
                  value: data['id'].toString(),
                  child: Center(child: Text(data['name'])),
                ),
              );
            }).toList();
          });
          popup();
        } else {
          SharedPreferences preferences = await SharedPreferences.getInstance();

          setState(() {
            isLoading = false;
          });

          await preferences.setStringList("birthday", [
            widget.details[0]['dob'],
            'pending',
          ]);
          widget.details.map((result) {
            setListString(
              id: result['id'].toString(),
              name: result['name'],
              class_name: result['class_name'],
              branch_name: result['branch_name'],
              section_name: result['section_name'],
              section_id: result['section_id'].toString(),
              class_id: result['class_id'].toString(),
              branch_id: result['branch_id'].toString(),
              type: result['type'].toString(),
              profile: result['profile'],
              bloodgroup: result['blood_group_name'],
              fathername: result['father_name'],
              mothername: result['mother_name'],
              primarynumber: result['primary_number'].toString(),
              secondarynumber: result['secondary_number'].toString(),
              year_id: result['academic_id'].toString(),
              status_type: '1',
            );
          }).toList();
        }

        //Get.to(const MainBoard());
      } else {
        textEditControl.text = '';

        isLoading = false;

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text("Invalid otp")));
      }
    }
  }

  String? _optionValue = '0';

  void popup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Center(child: Text("Select Student")),
              content: Container(
                height: 100,
                width: 200,
                child: SingleChildScrollView(
                  child: Column(
                    children:
                        widget.details.map<Widget>((result) {
                          String studentId = result['id'].toString();

                          return ListTile(
                            title: Row(
                              children: [
                                CircleAvatar(
                                  radius: 25,
                                  backgroundImage: NetworkImage(
                                    result['profile'],
                                  ),
                                  backgroundColor: Colors.transparent,
                                ),
                                SizedBox(width: 15),
                                Expanded(
                                  flex: 2, // Adjust the flex value as needed
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        result['name'],
                                        maxLines: 1,
                                        overflow: TextOverflow.fade,
                                      ),
                                      Text('Std-${result['class_name']}'),
                                    ],
                                  ),
                                ),
                                Radio<String>(
                                  value: studentId,
                                  groupValue: _optionValue,
                                  onChanged: (value) {
                                    setState(() {
                                      print('student-id');
                                      print(value);
                                      _optionValue = value!;
                                    });
                                  },
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
              actions: <Widget>[
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () async {
                    SharedPreferences preferences =
                        await SharedPreferences.getInstance();

                    await preferences.setStringList("birthday", [
                      widget.details[0]['dob'],
                      'pending',
                    ]);

                    widget.details.forEach((result) {
                      if (result['id'].toString() == _optionValue) {
                        // final notificationManager = FirebaseApi();

                        // notificationManager.multiple_user(
                        //     result['class_id'].toString(),
                        //     result['branch_id'].toString(),
                        //     result['id'].toString());

                        setListString(
                          id: result['id'].toString(),
                          name: result['name'],
                          class_name: result['class_name'],
                          branch_name: result['branch_name'],
                          section_name: result['section_name'],
                          section_id: result['section_id'].toString(),
                          class_id: result['class_id'].toString(),
                          branch_id: result['branch_id'].toString(),
                          type: result['type'].toString(),
                          profile: result['profile'],
                          bloodgroup: result['blood_group_name'],
                          fathername: result['father_name'],
                          mothername: result['mother_name'],
                          primarynumber: result['primary_number'].toString(),
                          secondarynumber:
                              result['secondary_number'].toString(),
                          year_id: result['academic_id'].toString(),
                          status_type: '2',
                        );
                      }
                    });

                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<bool> subback() async {
    exit(0);
  }

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: subback,
      child: Scaffold(
        key: _scaffoldKey,
        backgroundColor: Colors.white,

        body: Container(
          alignment: Alignment.center,
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 80,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  widget.title,
                  style: buttonStyle.copyWith(
                    color: Colors.black,
                    fontSize: 21,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 10),
                TextField(
                  controller: textEditControl,
                  style: const TextStyle(color: Colors.grey),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.zero,
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(7)),
                    ),
                    labelText: widget.lableText,
                    labelStyle: const TextStyle(color: Colors.black),
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 0,
                      minHeight: 0,
                    ),
                    prefixIcon: const Padding(
                      padding: EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 10,
                      ),
                      child: Text(
                        "",
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                GestureDetector(
                  onTap: () {
                    if (!isLoading) {
                      setState(() {
                        isLoading = true;

                        widget.buttonTitle == 'Login'
                            ? otpVerification()
                            : Future.delayed(Duration.zero, () async {
                              await Auth_User(textEditControl.text);
                            });
                      });
                    }
                  },
                  // onTap: widget.function,
                  child: Container(
                    height: 50,
                    width: MediaQuery.of(context).size.width - 80,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: appcolor,
                    ),
                    child:
                        !isLoading
                            ? Text(widget.buttonTitle, style: buttonStyle)
                            : Center(
                              child: CircularProgressIndicator(
                                color: Colors.white,
                              ),
                            ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

bool isTeacher = false;
TextEditingController textEditControl = TextEditingController();
