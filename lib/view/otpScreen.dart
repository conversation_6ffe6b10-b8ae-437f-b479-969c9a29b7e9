import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OtpScreen extends StatefulWidget {
  final String otp;
  final List details;
  const OtpScreen({super.key, required this.otp, required this.details});

  @override
  State<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends State<OtpScreen> {
  @override
  Widget build(BuildContext context) {
    print('build');
    print(widget.otp);
    return LoginScreen(
      title: "Login with OTP",
      lableText: "OTP",
      buttonTitle: "Login",
      function: () => otpVerification(),
      otp: widget.otp,
      details: widget.details,
    );
  }

  popup(context) {
    return showDialog(
      context: context,
      builder:
          (BuildContext context) => AlertDialog(
            title: const Text('AlertDialog Title'),
            content: const Text('AlertDialog description'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.pop(context, 'Cancel'),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, 'OK'),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  static Future setListString({
    required String id,
    required String name,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
    required String profile,
    required String bloodgroup,
    required String fathername,
    required String mothername,
    required String primarynumber,
    required String secondarynumber,
  }) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.clear();
    final prefs = await SharedPreferences.getInstance();
    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      bloodgroup,
      fathername,
      mothername,
      primarynumber,
      secondarynumber,
      type,
    ]);
  }

  static Future setTeacherDetails({
    required String id,
    required String name,
    required String profile,
    required String class_name,
    required String branch_name,
    required String section_name,
    required String section_id,
    required String class_id,
    required String branch_id,
    required String type,
  }) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    await preferences.clear();
    final prefs = await SharedPreferences.getInstance();
    prefs.setStringList('users', [
      id,
      name,
      class_name,
      branch_name,
      section_name,
      section_id,
      class_id,
      branch_id,
      profile,
      type,
    ]);
  }

  otpVerification() {
    if (textEditControl.text == "123456") {
      isTeacher = true;
      Get.to(const MainBoard());
    } else if (textEditControl.text == "654321") {
      isTeacher = false;
      Get.to(const MainBoard());
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text("Enter Valied Otp")));
    }
  }
}
