import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/view/auth/otp_screen.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  final TextEditingController _mobileController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _obscureText = true;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkExistingUser();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  Future<void> _checkExistingUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (prefs.getStringList('users')?.isNotEmpty == true) {
        Get.off(() => const MainBoard());
      }
    } catch (e) {
      // Continue with login flow
    }
  }

  Future<bool> _onWillPop() async {
    return await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                title: Text('Exit App', style: AppTextStyles.titleLarge),
                content: Text(
                  'Are you sure you want to exit?',
                  style: AppTextStyles.bodyMedium,
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: Text(
                      'Cancel',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () => exit(0),
                    child: Text(
                      'Exit',
                      style: AppTextStyles.labelLarge.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),
        ) ??
        false;
  }

  String? _validateMobile(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your mobile number';
    }
    if (value.length != 10) {
      return 'Mobile number must be 10 digits';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return 'Please enter a valid mobile number';
    }
    return null;
  }

  Future<void> _sendOTP() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _authenticateUser(_mobileController.text);
    } catch (e) {
      _showErrorSnackBar('Network error. Please try again.');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _authenticateUser(String mobile) async {
    final url = 'https://silverleafms.in/silvar_leaf/api/students/auth';
    final data = {'mobile': mobile};

    try {
      final response = await http.post(Uri.parse(url), body: data);
      final result = json.decode(response.body);

      if (result['status'] == 'success') {
        final List<dynamic> dataList = result['data'];
        final List<dynamic> filteredData = _filterCurrentAcademicYear(dataList);

        if (filteredData.isEmpty) {
          _showErrorSnackBar('No active student found for this mobile number');
        } else {
          Get.to(
            () =>
                OtpScreen(otp: result['otp'].toString(), details: filteredData),
          );
        }
      } else {
        _showErrorSnackBar('Invalid mobile number');
      }
    } catch (e) {
      _showErrorSnackBar('Network error. Please check your connection.');
    }
  }

  List<dynamic> _filterCurrentAcademicYear(List<dynamic> dataList) {
    final DateTime currentDate = DateTime.now();
    final int year = currentDate.year;
    final int month = currentDate.month;
    final int day = currentDate.day;

    return dataList.where((item) {
      if (item['academic_year'] != null) {
        final List<String> yearParts = item['academic_year'].toString().split(
          '-',
        );
        final int firstYear = int.parse(yearParts[0]);
        final int secondYear = int.parse(yearParts[1]);

        // For January through March and April 1-9
        if (year == secondYear && (month < 4 || (month == 4 && day < 10))) {
          return true;
        }
        // For April 10 through December
        else if (year == firstYear &&
            (month > 4 || (month == 4 && day >= 10))) {
          return true;
        }
      }
      return false;
    }).toList();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  @override
  void dispose() {
    _mobileController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: SizedBox(
                height:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom,
                child: Column(
                  children: [
                    SizedBox(height: 8.0.hp),

                    // Header Section
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.cardShadow,
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: Image.asset(
                                'images/logo.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Icon(
                                    Icons.school,
                                    size: 40,
                                    color: AppColors.primary,
                                  );
                                },
                              ),
                            ),
                          ),

                          SizedBox(height: 3.0.hp),

                          Text(
                            'Welcome Back',
                            style: AppTextStyles.displayMedium.copyWith(
                              color: AppColors.textOnPrimary,
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          SizedBox(height: 1.0.hp),

                          Text(
                            'Sign in to continue to Silverleaf',
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: AppColors.textOnPrimary.withOpacity(0.9),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 6.0.hp),

                    // Login Form
                    SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: AppColors.surface,
                            borderRadius: BorderRadius.circular(24),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.cardShadow,
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Text(
                                  'Mobile Number',
                                  style: AppTextStyles.labelLarge.copyWith(
                                    color: AppColors.textPrimary,
                                  ),
                                ),

                                SizedBox(height: 1.0.hp),

                                TextFormField(
                                  controller: _mobileController,
                                  keyboardType: TextInputType.phone,
                                  maxLength: 10,
                                  validator: _validateMobile,
                                  style: AppTextStyles.bodyLarge,
                                  decoration: InputDecoration(
                                    hintText: 'Enter your mobile number',
                                    hintStyle: AppTextStyles.bodyLarge.copyWith(
                                      color: AppColors.textHint,
                                    ),
                                    prefixIcon: const Icon(
                                      Icons.phone_android,
                                      color: AppColors.primary,
                                    ),
                                    counterText: '',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: AppColors.divider,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: AppColors.divider,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: AppColors.primary,
                                        width: 2,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: AppColors.error,
                                      ),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: const BorderSide(
                                        color: AppColors.error,
                                        width: 2,
                                      ),
                                    ),
                                    filled: true,
                                    fillColor: AppColors.surfaceVariant,
                                  ),
                                ),

                                SizedBox(height: 3.0.hp),

                                ElevatedButton(
                                  onPressed: _isLoading ? null : _sendOTP,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primary,
                                    foregroundColor: AppColors.textOnPrimary,
                                    padding: EdgeInsets.symmetric(vertical: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 0,
                                  ),
                                  child:
                                      _isLoading
                                          ? SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    AppColors.textOnPrimary,
                                                  ),
                                            ),
                                          )
                                          : Text(
                                            'Send OTP',
                                            style: AppTextStyles.buttonLarge,
                                          ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Footer
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          Text(
                            'By continuing, you agree to our',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textOnPrimary.withOpacity(0.8),
                            ),
                          ),
                          SizedBox(height: 0.5.hp),
                          Text(
                            'Terms of Service & Privacy Policy',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.textOnPrimary,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 2.0.hp),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
