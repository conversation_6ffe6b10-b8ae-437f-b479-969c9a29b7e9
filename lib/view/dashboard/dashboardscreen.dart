// ignore_for_file: avoid_unnecessary_containers, sized_box_for_whitespace

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/main.dart';
import 'package:silverleaf/view/User_Profile_Screen/profileScreen.dart';
import 'package:silverleaf/view/categoryListScreen/about_school.dart';
import 'package:silverleaf/view/categoryListScreen/calendae_of_events.dart';
import 'package:silverleaf/view/categoryListScreen/event_gallery.dart';
import 'package:silverleaf/view/categoryListScreen/fees_info.dart';
import 'package:silverleaf/view/categoryListScreen/attendance.dart';
// import 'package:silverleaf/view/categoryListScreen/kid's_attendance.dart';
import 'package:silverleaf/view/categoryListScreen/leaveRequest.dart';
import 'package:silverleaf/view/categoryListScreen/notification_panel.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/gallery/gallerymainscreen.dart';
import 'package:silverleaf/view/message/messagescreen.dart';
import 'package:silverleaf/widgets/chatWidgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:silverleaf/view/categoryListScreen/leavedetails.dart';

class DashBoardScreen extends StatefulWidget {
  const DashBoardScreen({super.key});

  @override
  State<DashBoardScreen> createState() => _DashBoardScreenState();
}

class _DashBoardScreenState extends State<DashBoardScreen> {
  List userNames = [];

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
          print('student');
        } else if (userNames.last == '2') {
          print('teacher');
          setState(() {
            isTeacher = true;
          });
        } else {
          print('failed');
        }
      });
    }
  }

  OverlayEntry? _overlayEntry;
  void initState() {
    _overlayEntry?.remove();
    super.initState();
    getListString();

    // final userList = getListString();
    // print('User List: $userList');
    //print(getListString());
    // getListString();
  }

  // void dispose() {
  //   print('Dashboard used');
  //   super.dispose();
  // }

  // void deactivate() {
  //   print('didactivate called');
  //   super.deactivate();
  // }
  void didChangeDependencies() {
    super.didChangeDependencies();
    //getListString();

    print('didUpdateWidget called');
  }

  void reloadState() {
    setState(() {
      // Resetting the counter for example
      print('test dashboard');
    });
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.9,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              // Your content for the draggable scrollable sheet
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    // Add your content here
                    Text('Your Draggable Scrollable Sheet Content'),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFFAFAFA), Color(0xFFFFFFFF)],
        ),
      ),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 2.0.hp),
            ProfileSection(),
            SizedBox(height: 3.0.hp),
            Birthday(),
            SizedBox(height: 3.0.hp),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.0.wp),
              child: Text(
                'Quick Actions',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 2.0.hp),
            GridSection(),
            SizedBox(height: 3.0.hp),
          ],
        ),
      ),
    );
  }
}

class GridSection extends StatefulWidget {
  const GridSection({super.key});

  @override
  State<GridSection> createState() => _GridSectionState();
}

class _GridSectionState extends State<GridSection> {
  List userNames = [];
  List itemTitleStudent = [
    'School Diary',
    "Leave Request ",
    "Calendar of Events",
    "Event Gallery",
    "Kid’s Attendance",
    'Fees \nInfo',
    'Message Dock',
    'About School',
    'Notification panel',
  ];

  List<IconData> itemIconStudent = [
    Icons.book_outlined, // School Diary
    Icons.event_note_outlined, // Leave Request
    Icons.calendar_month_outlined, // Calendar of Events
    Icons.photo_library_outlined, // Event Gallery
    Icons.how_to_reg_outlined, // Kid's Attendance
    Icons.account_balance_wallet_outlined, // Fees Info
    Icons.chat_bubble_outline, // Message Dock
    Icons.school_outlined, // About School
    Icons.notifications_outlined, // Notification panel
  ];

  List schoolcategoryScreens = [
    const DairyMainScreen(),
    // const LeaveDetails(),
    const LeaveRequest(),
    const CalenderOfEvents(),
    const GalleryMainScreen(),
    // const EventGallery(),
    const Attendance(),
    const FeesInfo(),

    const MessageMainScreen(),
    const AboutSchool(),

    const NotificationPanel(),
  ];

  var categoryindex = 0;
  //teacher catlogs
  List itemTitleteacher = [
    'School Diary',
    "Leave Request",
    "Calendar of Events",
    "Event Gallery",
    "Take Attendance",
    'About School',
    'Notification panel',
  ];
  List<IconData> itemIconteacher = [
    Icons.edit_note_outlined, // School Diary
    Icons.event_note_outlined, // Leave Request
    Icons.calendar_month_outlined, // Calendar of Events
    Icons.photo_library_outlined, // Event Gallery
    Icons.fact_check_outlined, // Take Attendance
    Icons.school_outlined, // About School
    Icons.notifications_outlined, // Notification panel
  ];
  List teachercategoryScreens = [
    const SchoolDairy(),
    const LeaveRequest(),
    const CalenderOfEvents(),
    const EventGallery(),
    const Attendance(),
    const AboutSchool(),
    const NotificationPanel(),
  ];

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      //  print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          setState(() {
            isTeacher = false;
          });
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        } else {}
      });
    }
  }

  void initState() {
    super.initState();
    // getListString();

    // final userList = getListString();
    // print('User List: $userList');
    //print(getListString());
    // getListString();
  }

  void didChangeDependencies() {
    super.didChangeDependencies();
    getListString();

    print('didUpdateWidget called dashboard');
  }

  void dispose() {
    print('dashboard closed');
    super.dispose();
  }

  void _showBottomSheet() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          minChildSize: 0.1,
          maxChildSize: 0.9,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              // Your content for the draggable scrollable sheet
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    // Add your content here
                    Text('Your Draggable Scrollable Sheet Content'),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.0.wp),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        itemCount:
            isTeacher == false
                ? itemTitleStudent.length
                : itemTitleteacher.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 4.0.wp,
          mainAxisSpacing: 3.0.hp,
          childAspectRatio: 0.85,
        ),
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                categoryindex = index;
              });
              isTeacher == false
                  ? Get.to(schoolcategoryScreens[index])
                  : Get.to(teachercategoryScreens[index]);
            },
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.cardShadow,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 12.0.wp,
                    height: 12.0.wp,
                    padding: EdgeInsets.all(2.5.wp),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primaryLight.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.primary.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      isTeacher == true
                          ? itemIconteacher[index]
                          : itemIconStudent[index],
                      size: 6.0.wp,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 1.5.hp),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 2.0.wp),
                    child: Text(
                      isTeacher == false
                          ? itemTitleStudent[index]
                          : itemTitleteacher[index],
                      style: AppTextStyles.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class Birthday extends StatefulWidget {
  const Birthday({super.key});

  @override
  State<Birthday> createState() => _BirthdayState();
}

class _BirthdayState extends State<Birthday> {
  var products = [];

  List userNames = [];

  Future<List> fetchApiData() async {
    print(userNames[14]);
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/students/student-birthday/${userNames.last}/${userNames[14]}',
      ),
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print('api response data');

      List<dynamic> jsonData = responseBody['data'];
      products.clear();
      setState(() {
        jsonData.forEach((result) {
          if (result['type'] == 1) {
            products.add({
              'name': result['name'],
              'class_name': result['class_name'],
              'section_name': result['section_name'],
              'profile': result['profile'],
              'type': 1,
            });
          } else {
            products.add({
              'name': result['name'],
              'profile': result['profile'],
              'type': 2,
            });
          }
        });
      });

      //print(this.jsonData);
      return jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchApiData();
      });
    }
  }

  void initState() {
    super.initState();
    getListString();
  }

  @override
  Widget build(BuildContext context) {
    if (products.isNotEmpty) {
      return Container(
        margin: EdgeInsets.symmetric(horizontal: 6.0.wp),
        padding: EdgeInsets.all(4.0.wp),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFE8F5E8), Color(0xFFF1F8E9)],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow,
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2.0.wp),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.cake,
                    color: AppColors.textOnPrimary,
                    size: 5.0.wp,
                  ),
                ),
                SizedBox(width: 3.0.wp),
                Text(
                  "Today's Birthdays",
                  style: AppTextStyles.titleLarge.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 3.0.hp),
            SizedBox(
              height: 12.0.hp,
              child: CarouselSlider(
                options: CarouselOptions(
                  height: 12.0.hp,
                  enlargeCenterPage: true,
                  autoPlay: true,
                  aspectRatio: 16 / 9,
                  autoPlayCurve: Curves.fastOutSlowIn,
                  enableInfiniteScroll: true,
                  autoPlayAnimationDuration: const Duration(milliseconds: 800),
                  viewportFraction: 0.85,
                ),
                items:
                    products.map((product) {
                      return Builder(
                        builder: (BuildContext context) {
                          return Container(
                            width: MediaQuery.of(context).size.width,
                            margin: EdgeInsets.symmetric(horizontal: 5.0),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8.0),
                              border: Border.all(
                                color:
                                    Colors
                                        .white, // You can set the color of the border here
                                width:
                                    1.0, // You can set the width of the border here
                              ),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      top: 8.0,
                                      bottom: 8.0,
                                      left: 25.0,
                                    ), // Adjust top and bottom padding as needed
                                    child: CircleAvatar(
                                      radius: 40.0.sp,
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          80.0,
                                        ),
                                        child: Image.network(
                                          product['profile'],
                                          width: double.infinity,
                                          height: double.infinity,
                                          fit: BoxFit.cover,
                                          errorBuilder: (
                                            context,
                                            error,
                                            stackTrace,
                                          ) {
                                            return Image.asset(
                                              'images/user.png',
                                              width: double.infinity,
                                              height: double.infinity,
                                            );
                                          },
                                        ),
                                      ),
                                    ),

                                    // Image.network(
                                    //   product['profile'],
                                    //   //  fit: BoxFit.cover,
                                    //   width: double.infinity,
                                    //   height: double.infinity,
                                    // ),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 20.0,
                                    ),
                                    child: Text(
                                      product['type'] == 1
                                          ? '${product['name']}\nstd-${product['class_name']},${product['section_name']}'
                                          : '${product['name']}\nTeacher',
                                      style: TextStyle(fontSize: 18.0),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container();
    }
  }
}

class ProfileSection extends StatefulWidget {
  const ProfileSection({super.key});

  @override
  State<ProfileSection> createState() => _ProfileSectionState();
}

class _ProfileSectionState extends State<ProfileSection> {
  List userNames = [];
  save_button() {
    print('save button clicked');
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
          print('student');
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
          print('teacher');
        } else {
          print('failed');
        }
      });
    }
  }

  void initState() {
    super.initState();

    getListString();
  }

  void dispose() {
    print('dispose');
    // if (_isDropdownOpen) {
    //   _overlayEntry?.remove();
    // }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return userNames.isEmpty
        ? Center(child: CircularProgressIndicator(color: AppColors.primary))
        : Container(
          margin: EdgeInsets.symmetric(horizontal: 6.0.wp),
          padding: EdgeInsets.all(4.0.wp),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [AppColors.primary, Color(0xFF4CAF50)],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow,
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: GestureDetector(
            onTap: () {
              Get.to(
                ProfileScreen(
                  student_profile: userNames[8],
                  student_name: userNames[1],
                  student_class: userNames[2],
                  student_section: userNames[4],
                  student_branch: userNames[3],
                  student_blood_group: userNames[9],
                  student_father_name: userNames[10],
                  student_mother_name: userNames[11],
                  student_primary_contact_no: userNames[12],
                  student_secondary_contact_no: userNames[13],
                  id: userNames[0],
                ),
              );
            },
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.textOnPrimary.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 8.0.wp,
                    backgroundColor: AppColors.surface,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8.0.wp),
                      child: Image.network(
                        userNames[8],
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.person,
                            size: 8.0.wp,
                            color: AppColors.primary,
                          );
                        },
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 4.0.wp),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userNames[1],
                        style: AppTextStyles.titleLarge.copyWith(
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 0.5.hp),
                      Text(
                        'Class ${userNames[2]} - ${userNames[4]}',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textOnPrimary.withOpacity(0.9),
                        ),
                      ),
                      SizedBox(height: 0.5.hp),
                      Text(
                        userNames[3],
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textOnPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),

                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textOnPrimary.withOpacity(0.7),
                  size: 4.0.wp,
                ),
              ],
            ),
          ),
        );
  }
}
