// ignore_for_file: prefer_const_constructors, avoid_unnecessary_containers

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';
import 'package:silverleaf/controller/gallery_controller.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:ui' as ui;

import 'package:flutter/rendering.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:dio/dio.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:galleryimage/galleryimage.dart';
import 'dart:typed_data';
import 'package:photo_view/photo_view.dart';

var gallerypages = 0;

class GalleryMainScreen extends StatefulWidget {
  const GalleryMainScreen({super.key});

  @override
  State<GalleryMainScreen> createState() => _GalleryMainScreenState();
}

class _GalleryMainScreenState extends State<GalleryMainScreen> {
  //GalleryController galleryController = GalleryController();
  var jsonData;
  var list_data;
  List userNames = [];
  void initState() {
    // this.fetchApiData();
    super.initState();
    this.getListString();
    //this.jsonData = galleryController.fetchApiData();
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;
        this.fetchApiData();
      });
    }
  }

  Future<List> fetchApiData() async {
    print(userNames[14]);
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-details/${userNames[14]}',
      ),
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      this.jsonData = responseBody['data'];
      //print(this.jsonData);
      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  back() {
    // print('fffff');
    // setState(() {
    //   globalBottomBarIndex = 0;
    //   Get.forceAppUpdate();
    //   Get.back();
    // });

    Get.back();
  }

  List galleryImage = ['images/inde.jpg', 'images/culturalsevent.jpg'];
  List galleryImageTitle = ['Independence Day', 'Cultural Event'];
  List subtitles = [
    'Independence day Flag Raising and Photos......',
    'Inter - Coimbatore Cultural Event Photos....',
  ];
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: CustomizedAppBar(
            back: back,
            profile: () {},
            screenName: 'Gallery',
            screen_id: 2,
          ),
        ),
        body: galleryList(),
      ),
    );
  }

  Widget galleryList() {
    return Container(
      child: FutureBuilder<List<dynamic>>(
        future: fetchApiData(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: CircularProgressIndicator());
          } else {
            List<dynamic> data = snapshot.data!;
            if (snapshot.data!.length == 0) {
              return Center(child: Text('No Data'));
            } else {
              return ListView.separated(
                separatorBuilder: (context, index) {
                  return Divider();
                },
                itemCount: snapshot.data!.length,
                shrinkWrap: true,
                padding: EdgeInsets.all(17.0.sp),
                itemBuilder: (context, index) {
                  final item = data[index];
                  if (snapshot.data!.length == 0) {
                    return Center(child: Text('No Data'));
                  } else {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          // gallerypages = 1;
                          Get.to(
                            GallerySecondScreen(
                              dataToReceive:
                                  this.jsonData[index]['album_title'],
                              description: this.jsonData[index]['description'],
                              user_id: this.jsonData[index]['id'],
                            ),
                          );
                        });
                      },
                      child: Container(
                        child: Row(
                          children: [
                            Container(
                              height: 15.0.hp,
                              width: 30.0.wp,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: NetworkImage(
                                    this.jsonData[index]['gallery_image'],
                                  ),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            Expanded(
                              child: SizedBox(
                                height: 15.0.hp,
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        this.jsonData[index]['album_title'],
                                        style: dairyTextStyle.copyWith(
                                          color: Color(0xff12827A),
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14.0.sp,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          this.jsonData[index]['description']
                                              .toString(),
                                          // style: dairyTextStyle.copyWith(
                                          //   fontWeight: FontWeight.w500,
                                          //   fontSize: 9.0.sp,
                                          //   overflow: TextOverflow.ellipsis,
                                          //   maxLines: 2,
                                          // ),
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontSize: 9.0.sp,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 2,
                                        ),
                                      ),
                                      SizedBox(
                                        child: Row(
                                          children: [
                                            Row(
                                              children: [
                                                SizedBox(
                                                  width: 10.0.wp,
                                                  child: Stack(
                                                    children: [
                                                      peopleContainer(),
                                                      Positioned(
                                                        left: 6,
                                                        child:
                                                            peopleContainer(),
                                                      ),
                                                      Positioned(
                                                        left: 13,
                                                        child:
                                                            peopleContainer(),
                                                      ),
                                                      Positioned(
                                                        left: 20,
                                                        child:
                                                            peopleContainer(),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Text(
                                                  " +${this.jsonData[index]['gallery_image_count']} Photos",
                                                  style: dairyTextStyle
                                                      .copyWith(
                                                        color: appcolor,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 9.0.sp,
                                                      ),
                                                ),
                                              ],
                                            ),
                                            Expanded(child: SizedBox()),
                                            Text(
                                              "See All >>",
                                              style: dairyTextStyle.copyWith(
                                                color: Colors.black,
                                                fontWeight: FontWeight.w900,
                                                fontSize: 9.0.sp,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }
                },
              );
            }
          }
        },
      ),
    );
  }

  Widget peopleContainer() {
    return Container(
      height: 2.6.hp,
      width: 4.0.wp,
      decoration: BoxDecoration(
        color: appcolor,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1),
      ),
    );
  }
}

class GallerySecondScreen extends StatefulWidget {
  final String dataToReceive;
  final String description;
  final int user_id;

  const GallerySecondScreen({
    super.key,
    required this.dataToReceive,
    required this.description,
    required this.user_id,
  });
  // GallerySecondScreen({
  //   super.key,
  // });

  @override
  State<GallerySecondScreen> createState() => _GallerySecondScreenState();
}

class _GallerySecondScreenState extends State<GallerySecondScreen> {
  String text = '';
  String description = '';

  subback() {
    // Get.to(const MainBoard());
    Get.back();
  }

  GalleryController galleryController = GalleryController();
  Future<List> fetchgalleryData(int id) async {
    print('tt');
    final url =
        'https://silverleafms.in/silvar_leaf/api/gallery/gallery-all-images/$id';

    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      final result = json.decode(response.body);

      return result['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }

  String? firstHalf;
  String? secondHalf;

  bool flag = true;
  bool isExpanded = false;
  @override
  void initState() {
    super.initState();
    this.description = widget.description;
    this.text = widget.dataToReceive;

    //final response = this.galleryController.fetchgalleryData(widget.user_id);

    this.fetchgalleryData(widget.user_id);

    // if (this.description.length > 50) {
    //   firstHalf = this.description.substring(0, 90);
    //   secondHalf = this.description.substring(50, this.description.length);
    // } else {
    //   firstHalf = this.description;
    //   secondHalf = "";
    // }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () => subback(),
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: PreferredSize(
            preferredSize: Size(double.infinity, 9.0.hp),
            child: CustomizedAppBar(
              back: () {
                subback();
              },
              profile: () {},
              screenName: "Gallery",
              screen_id: 2,
            ),
          ),
          body: Container(
            padding: EdgeInsets.all(17.0.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  this.text,
                  style: dairyTextStyle.copyWith(
                    color: Color(0xff12827A),
                    fontWeight: FontWeight.w600,
                    fontSize: 14.0.sp,
                  ),
                ),
                SizedBox(height: 4),
                Container(
                  child: Text(
                    this.description!,
                    maxLines:
                        isExpanded ? null : 3, // Display 3 lines initially
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                  child: Text(
                    isExpanded ? 'Read More' : 'Read Less',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 2.0.hp),
                Divider(),
                SizedBox(height: 2.0.hp),
                FutureBuilder<List<dynamic>>(
                  future: this.fetchgalleryData(widget.user_id),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return CircularProgressIndicator(); // Loading indicator
                    } else if (snapshot.hasError) {
                      return Text('Error: ${snapshot.error}');
                    } else {
                      List<dynamic> data = snapshot.data!;
                      return Expanded(
                        child: GridView.builder(
                          shrinkWrap: true,
                          itemCount: snapshot.data!.length,
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisSpacing: 10,
                                mainAxisSpacing: 10,
                                crossAxisCount: 2,
                              ),
                          itemBuilder: (context, index) {
                            final item = data[index];
                            return GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder:
                                        (context) => GalleryView(
                                          imagesData: data,
                                          current: index,
                                        ),
                                  ),
                                );
                              },
                              child: Container(
                                width: 100, // Set the desired width
                                height: 100, // Set the desired height
                                child: Image.network(
                                  item['image_path'],
                                  fit:
                                      BoxFit
                                          .cover, // Adjust the fit as per your requirement
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget popup(String image) {
    return GestureDetector(
      onLongPress: () {
        save_image(image);
        showDownloadMessage(context);
      },
      child: Hero(
        tag: 'fullScreenImage',
        child: Image.network(image, fit: BoxFit.contain),
      ),
    );
  }

  void showSnackBar(BuildContext context) {
    final snackBar = SnackBar(content: Text('Download button pressed!'));

    // Use the ScaffoldMessenger to show the SnackBar
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void showDownloadMessage(BuildContext context) {
    OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).size.height * 0.1,
            // left: MediaQuery.of(context).size.width * 0.25,
            left: 0.0,
            right: 0.0,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Center(
                  child: Text(
                    'Image Downloaded!',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context)?.insert(overlayEntry);

    // Delayed removal of the overlay
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  save_image(image) async {
    // var response = await Dio()
    //     .get(image, options: Options(responseType: ResponseType.bytes));
    // final result = await ImageGallerySaver.saveImage(
    //     Uint8List.fromList(response.data),
    //     quality: 60,
    //     name: "gallery image");

    var response = await Dio().get(
      image,
      options: Options(responseType: ResponseType.bytes),
    );
    final result = await ImageGallerySaverPlus.saveImage(
      Uint8List.fromList(response.data),
      quality: 60,
      name: "gallery image",
    );
    print(result);
  }
}

class ZoomableImageScreen extends StatelessWidget {
  final String image;

  ZoomableImageScreen({required this.image});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: PhotoView(
          backgroundDecoration: BoxDecoration(
            color: Colors.white, // Ensure the PhotoView background is white
          ),
          imageProvider: NetworkImage(image),
          minScale: PhotoViewComputedScale.contained * 0.8,
          maxScale: PhotoViewComputedScale.covered * 2,
          enableRotation: true,
        ),
      ),
    );
  }
}

class GalleryView extends StatefulWidget {
  final List<dynamic> imagesData;
  int current;

  GalleryView({required this.imagesData, required this.current});

  @override
  _GalleryViewState createState() => _GalleryViewState();
}

class _GalleryViewState extends State<GalleryView> {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.current;

    _pageController = PageController(initialPage: _currentPage);
  }

  @override
  Widget build(BuildContext context) {
    print('gallery-view');
    return Scaffold(
      body: Container(
        color: Colors.white, // Set the background color to white
        child: PageView.builder(
          controller: _pageController,
          itemCount: widget.imagesData.length,
          onPageChanged: (index) {
            setState(() {
              _currentPage = index;
              widget.current = index;
            });
          },
          itemBuilder: (context, index) {
            return popup(widget.imagesData[index]['image_path'], index);
          },
        ),
      ),
    );
    // PageView.builder(
    //   controller: _pageController,
    //   itemCount: widget.imagesData.length,
    //   onPageChanged: (index) {
    //     setState(() {
    //       _currentPage = index;
    //       widget.current = index;
    //     });
    //   },
    //   itemBuilder: (context, index) {
    //     return popup(widget.imagesData[index]['image_path'], index);
    //   },
    // );
  }

  Widget popup(String image, index) {
    return GestureDetector(
      onLongPress: () {
        save_image(image);
        showDownloadMessage(context);
      },
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ZoomableImageScreen(image: image),
          ),
        );
      },
      child: Container(
        color: Colors.white,
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: Hero(
          tag: index,
          child: PhotoView(
            backgroundDecoration: BoxDecoration(
              color: Colors.white, // Ensure the PhotoView background is white
            ),
            imageProvider: NetworkImage(image),
            minScale: PhotoViewComputedScale.contained * 0.8,
            maxScale: PhotoViewComputedScale.covered * 2,
            enableRotation: true,
          ),
        ),

        // Image.network(
        //   image,
        //   fit: BoxFit.contain,
        // ),
      ),
    );
  }

  void showDownloadMessage(BuildContext context) {
    OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).size.height * 0.1,
            // left: MediaQuery.of(context).size.width * 0.25,
            left: 0.0,
            right: 0.0,
            child: Material(
              color: Colors.transparent,
              child: Container(
                padding: EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Center(
                  child: Text(
                    'Image Downloaded!',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context)?.insert(overlayEntry);

    // Delayed removal of the overlay
    Future.delayed(Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  save_image(image) async {
    // var response = await Dio()
    //     .get(image, options: Options(responseType: ResponseType.bytes));
    // final result = await ImageGallerySaver.saveImage(
    //     Uint8List.fromList(response.data),
    //     quality: 60,
    //     name: "gallery image");

    var response = await Dio().get(
      image,
      options: Options(responseType: ResponseType.bytes),
    );
    final result = await ImageGallerySaverPlus.saveImage(
      Uint8List.fromList(response.data),
      quality: 60,
      name: "gallery image",
    );
    print(result);
  }
}
