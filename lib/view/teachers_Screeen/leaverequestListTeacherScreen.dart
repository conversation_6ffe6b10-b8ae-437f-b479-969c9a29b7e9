// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
// import 'package:silverleaf/view/dairy/dairymainscreen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'package:intl/intl.dart';

import '../../contest/color.dart';

class LeaveRequestListTeacherScreen extends StatefulWidget {
  const LeaveRequestListTeacherScreen({super.key});

  @override
  State<LeaveRequestListTeacherScreen> createState() =>
      _LeaveRequestListTeacherScreenState();
}

class _LeaveRequestListTeacherScreenState
    extends State<LeaveRequestListTeacherScreen> {
  List userNames = [];
  var jsonData;
  var student_leave_type;
  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;
        this.list_leave();
      });
    }
  }

  Future<List> list_leave() async {
    // print(userNames[7]);
    // print(userNames[6]);
    // print(userNames[5]);
    final response = await http.get(
      Uri.parse(
        'https://silverleafms.in/silvar_leaf/api/leave/view-student/${userNames[7]}/${userNames[6]}/${userNames[5]}',
      ),
    );

    if (response.statusCode == 200) {
      print(response.body);
      var responseBody = json.decode(response.body);
      this.jsonData = responseBody['data'];
      // print(this.jsonData);
      return this.jsonData;
    } else {
      throw Exception('Failed to load data');
    }
  }

  void initState() {
    super.initState();
    this.getListString();
    //this.list_leave();
  }

  String convertDateFormat(String originalDate, String type) {
    // Parse the original date string
    String formattedDate = '';

    DateTime parsedDate = DateFormat('dd/MM/yyyy').parseStrict(originalDate);

    // Format the parsed date as desired
    if (type == 'post_date') {
      formattedDate = DateFormat('dd MMM yyyy | EEEE').format(parsedDate);
    } else {
      formattedDate = DateFormat('dd MMM yyyy').format(parsedDate);
    }

    return formattedDate;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 10.0.hp),
          child: CustomizedAppBar(
            back: () {
              Get.back();
            },
            profile: () {},
            screenName: "Leave Request",
            screen_id: 2,
          ),
        ),
        body: WillPopScope(
          onWillPop: () => back(),
          child: Column(
            children: [
              const SizedBox(),
              Expanded(
                child: Container(
                  child: FutureBuilder<List<dynamic>>(
                    future: list_leave(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Center(child: CircularProgressIndicator());
                      } else if (snapshot.hasError) {
                        return Center(child: CircularProgressIndicator());
                      } else {
                        List<dynamic> data = snapshot.data!;

                        return ListView.separated(
                          separatorBuilder: (context, index) {
                            return const Divider();
                          },
                          itemCount: snapshot.data!.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            final item = data[index];
                            String formattedDate = convertDateFormat(
                              item['posted_date'],
                              'post_date',
                            );

                            String fromDate = convertDateFormat(
                              item['from_date'],
                              'leave_date',
                            );
                            String toDate = convertDateFormat(
                              item['to_date'],
                              'to_date',
                            );

                            if (item['leave_type'] == 4) {
                              this.student_leave_type = 'Family Function';
                            } else if (item['leave_type'] == 2) {
                              this.student_leave_type = 'Sick';
                            } else if (item['leave_type'] == 3) {
                              this.student_leave_type = 'Personal';
                            } else if (item['leave_type'] == 5) {
                              this.student_leave_type = 'Others';
                            }

                            return GestureDetector(
                              onTap: () {
                                // Get.to(DairySecondScreenDetails());
                                // Get.to(const DairySubPage());
                              },
                              child: Container(
                                color:
                                    index < 3
                                        ? Color(0xffF4FFEE)
                                        : Colors.transparent,
                                child: Padding(
                                  padding: const EdgeInsets.all(20.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        formattedDate,
                                        style: dairyTextStyle.copyWith(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w900,
                                          color: Colors.black,
                                        ),
                                      ),
                                      SizedBox(height: 2),
                                      SizedBox(
                                        child: Row(
                                          children: [
                                            Text(
                                              item['student_name'],
                                              style: dairyTextStyle.copyWith(
                                                color: subappcolor,
                                                fontSize: 18,
                                                fontWeight: FontWeight.w700,
                                              ),
                                            ),
                                            Expanded(child: SizedBox()),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Text(
                                                  "From - ${fromDate}",
                                                  style: dairyTextStyle
                                                      .copyWith(
                                                        color: Colors.grey,
                                                      ),
                                                ),
                                                Text(
                                                  "to - ${toDate}",
                                                  style: dairyTextStyle
                                                      .copyWith(
                                                        color: Colors.grey,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        child: Text(
                                          student_leave_type,
                                          style: dairyTextStyle.copyWith(
                                            fontSize: 14.0.sp,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                      SizedBox(height: .4.hp),
                                      Text(
                                        item['description'],
                                        style: dairyTextStyle.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      }
                    },
                  ),
                ),
              ),

              // Expanded(
              //   child: Container(
              //     child: ListView.separated(
              //       separatorBuilder: (context, index) {
              //         return const Divider();
              //       },
              //       itemCount: 10,
              //       shrinkWrap: true,
              //       itemBuilder: (context, index) {
              //         return GestureDetector(
              //           onTap: () {
              //             // Get.to(const DairySecondScreenDetails());
              //             // Get.to(const DairySubPage());
              //           },
              //           child: Container(
              //             color: index < 3
              //                 ? Color(0xffF4FFEE)
              //                 : Colors.transparent,
              //             child: Padding(
              //               padding: const EdgeInsets.all(20.0),
              //               child: Column(
              //                 crossAxisAlignment: CrossAxisAlignment.start,
              //                 children: [
              //                   Text(
              //                     "12 Aug, 2023 | Saturday",
              //                     style: dairyTextStyle.copyWith(
              //                         fontSize: 12,
              //                         fontWeight: FontWeight.w900,
              //                         color: Colors.black),
              //                   ),
              //                   SizedBox(
              //                     height: 2,
              //                   ),
              //                   SizedBox(
              //                     child: Row(
              //                       children: [
              //                         Text(
              //                           "Abinav. S",
              //                           style: dairyTextStyle.copyWith(
              //                               color: subappcolor,
              //                               fontSize: 18,
              //                               fontWeight: FontWeight.w700),
              //                         ),
              //                         Expanded(child: SizedBox()),
              //                         Column(
              //                           crossAxisAlignment:
              //                               CrossAxisAlignment.end,
              //                           children: [
              //                             Text(
              //                               "From - 12, sept 23",
              //                               style: dairyTextStyle.copyWith(
              //                                 color: Colors.grey,
              //                               ),
              //                             ),
              //                             Text(
              //                               "to - 16, sept 23",
              //                               style: dairyTextStyle.copyWith(
              //                                 color: Colors.grey,
              //                               ),
              //                             ),
              //                           ],
              //                         )
              //                       ],
              //                     ),
              //                   ),
              //                   SizedBox(
              //                     child: Text(
              //                       "Planned Leave",
              //                       style: dairyTextStyle.copyWith(
              //                           fontSize: 14.0.sp,
              //                           fontWeight: FontWeight.w600),
              //                     ),
              //                   ),
              //                   SizedBox(
              //                     height: .4.hp,
              //                   ),
              //                   Text(
              //                     "Prepare the globe model with painting on ball. The will be allotted according to the Creative.",
              //                     style: dairyTextStyle.copyWith(
              //                         fontWeight: FontWeight.w500),
              //                   ),
              //                 ],
              //               ),
              //             ),
              //           ),
              //         );
              //       },
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
