// ignore_for_file: prefer_interpolation_to_compose_strings, prefer_const_constructors, avoid_print

import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/view/appbar/customizedappbar.dart';
import 'package:silverleaf/view/categoryListScreen/leaveRequest.dart';
import 'package:silverleaf/view/categoryListScreen/school_Dairy.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:silverleaf/controller/teacher_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';

class TeacherDairyEntreyScreen extends StatefulWidget {
  const TeacherDairyEntreyScreen({super.key});

  @override
  State<TeacherDairyEntreyScreen> createState() =>
      _TeacherDairyEntreyScreenState();
}

class _TeacherDairyEntreyScreenState extends State<TeacherDairyEntreyScreen> {
  List userNames = [];
  String? selectedValue;
  // final TeacherController teacherController = TeacherController();

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      print(storedUserNames);

      setState(() {
        userNames = storedUserNames;
      });
    }
  }

  list_subject() async {
    final response = await http.get(
      Uri.parse('https://silverleafms.in/silvar_leaf/api/masters/subjects'),
    );

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print(responseBody['data']);
      setState(() {
        List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
          responseBody['data'],
        );

        jsonData.map((data) {
          items.add(
            DropdownMenuItem(
              value: data['id'].toString(),
              child: Text(data['subject_name']),
            ),
          );
        }).toList();
      });
    } else {
      throw Exception('Failed to load data');
    }
  }

  save_diary(context) async {
    if (selectedValue != '0') {
      final url =
          'https://silverleafms.in/silvar_leaf/api/dairy/save-diary'; // Replace with the actual URL of your PHP script.
      final data = {
        'class_id': userNames[6],
        'section_id': userNames[5],
        'teacher_id': userNames[0],
        'subject_id': selectedValue,
        'title': title.text,
        'description': description.text,
        'date': _selectedDate,
        'given_date': '22-09-1999',
        'role': '2',
      };
      print(data);
      final response = await http.post(Uri.parse(url), body: data);
      print(json.decode(response.body));

      return showDialog(
        context: context,
        useSafeArea: true,
        builder: (BuildContext context) {
          return Dialog(
            child: Container(
              height: 20.0.hp,
              alignment: Alignment.center,
              child: const Text(
                "Success \nYour diary data saved",
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      );
    }
  }

  List<DropdownMenuItem<String>> items = [];

  void initState() {
    super.initState();
    getListString();
    selectedValue = '0';
    items.add(DropdownMenuItem(value: '0', child: Text('Select')));
    list_subject();
  }

  TextEditingController title = TextEditingController();
  TextEditingController description = TextEditingController();
  TextEditingController given_date = TextEditingController();
  TextEditingController todate = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: Size(double.infinity, 9.0.hp),
        child: CustomizedAppBar(
          back: back,
          profile: () {},
          screenName: 'School Dairy',
          screen_id: 2,
        ),
      ),
      body: Container(
        padding: EdgeInsets.all(10.0.sp),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 1.0.hp),
              Text(
                "Select Subject",
                style: textStyle.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 12.0.sp,
                ),
              ),
              SizedBox(height: 1.0.hp),
              // DropdownMenuExample(),
              DropdownButton<String>(
                isExpanded: true,
                value: selectedValue,
                items: items,
                onChanged: (String? value) {
                  setState(() {
                    if (value != '') {
                      selectedValue = value!;
                    } // Update the selected value.
                  });
                },
              ),
              SizedBox(height: 3.0.hp),
              Text(
                "tItle",
                style: textStyle.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 12.0.sp,
                ),
              ),
              SizedBox(height: 1.0.hp),
              TextField(
                controller: title,
                style: const TextStyle(color: Colors.grey),
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.zero,
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(7)),
                  ),
                  labelStyle: const TextStyle(color: Colors.black),
                  prefixIconConstraints: const BoxConstraints(
                    minWidth: 0,
                    minHeight: 0,
                  ),
                  prefixIcon: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                    child: Text(
                      "",
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 3.0.hp),

              // Text(
              //   "tITLE",
              //   style: textStyle.copyWith(
              //       color: Colors.black,
              //       fontWeight: FontWeight.w400,
              //       fontSize: 12.0.sp),
              // ),
              // SizedBox(
              //   height: 1.0.hp,
              // ),
              // Container(
              //   decoration: BoxDecoration(
              //       border: Border.all(
              //         color: Colors.grey,
              //         width: 1,
              //       ),
              //       borderRadius: BorderRadius.circular(5.0.sp)),
              //   height: 100,
              //   child: TextField(
              //     controller: description,
              //     maxLines: null,
              //     expands: true,
              //     keyboardType: TextInputType.multiline,
              //   ),
              // ),

              // SizedBox(
              //   height: 3.0.hp,
              // ),
              Text(
                "Description",
                style: textStyle.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 12.0.sp,
                ),
              ),
              SizedBox(height: 1.0.hp),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey, width: 1),
                  borderRadius: BorderRadius.circular(5.0.sp),
                ),
                height: 200,
                child: TextField(
                  controller: description,
                  maxLines: null,
                  expands: true,
                  keyboardType: TextInputType.multiline,
                ),
              ),
              SizedBox(height: 3.0.hp),
              Text(
                "Select the DOS",
                style: textStyle.copyWith(
                  color: Colors.black,
                  fontWeight: FontWeight.w400,
                  fontSize: 12.0.sp,
                ),
              ),
              SizedBox(height: 3.0.hp),
              SizedBox(
                height: 6.0.hp,
                width: MediaQuery.of(context).size.width,
                child: GestureDetector(
                  onTap: () => showMyDialog(context),
                  child: dateContainer(
                    title: showStartingDate == '' ? "" : showStartingDate,
                  ),
                ),
              ),
              SizedBox(height: 2.0.hp),
              GestureDetector(
                onTap: () {
                  save_diary(context);
                  //Get.back();
                  //  teacherController.increment();
                },
                child: Container(
                  height: 6.0.hp,
                  width: MediaQuery.of(context).size.width - 20,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: appcolor,
                    borderRadius: BorderRadius.circular(7.0.sp),
                  ),
                  child: Text(
                    "Submit",
                    style: textStyle.copyWith(
                      color: Colors.white,
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget dateContainer({title}) {
    return Container(
      height: 4.0.hp,
      width: 25.0.wp,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: .2.wp),
        borderRadius: BorderRadius.circular(5.0.sp),
      ),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Text(
              title,
              style: textStyle.copyWith(color: Colors.black, fontSize: 10.0.sp),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Icon(Icons.edit_calendar_outlined, size: 18.0.sp),
          ),
        ],
      ),
    );
  }

  final DateRangePickerController _controller = DateRangePickerController();
  showMyDialog(context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            height: 50.0.hp,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SfDateRangePicker(
                  view: DateRangePickerView.month,
                  selectionMode: DateRangePickerSelectionMode.single,
                  initialSelectedDate: DateTime.now(),
                  showNavigationArrow: true,
                  controller: _controller,
                  onSelectionChanged: _onSelectionChanged,
                ),
                TextButton(
                  child: const Text('Submit'),
                  onPressed: () {
                    setState(() {
                      showStartingDate = _selectedDate;
                      Navigator.of(context).pop();
                    });
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // leaveRequestPopUp(context) {
  //   return showDialog(
  //     context: context,
  //     useSafeArea: true,
  //     builder: (BuildContext context) {
  //       return Dialog(
  //         child: Container(
  //           height: 20.0.hp,
  //           alignment: Alignment.center,
  //           child: Text(
  //             "Your Leave have been \nRequested Successfully ",
  //             textAlign: TextAlign.center,
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  String _selectedDate = '';
  DateTime? selectedDateandYear;
  String _dateCount = '';
  String _range = '';
  String showStartingDate = '';
  String showEndDate = '';
  String _rangeCount = '';
  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    setState(() {
      if (args.value is PickerDateRange) {
        _range =
            '${DateFormat('dd/MM/yyyy').format(args.value.startDate)} -'
            ' ${DateFormat('dd/MM/yyyy').format(args.value.endDate ?? args.value.startDate)}';
        print("Range" + _range);
      } else if (args.value is DateTime) {
        _selectedDate = args.value.toString();
        selectedDateandYear = args.value;
        _selectedDate = DateFormat('yyyy-MM-dd').format(args.value);
        print("Select" + _selectedDate);
      } else if (args.value is List<DateTime>) {
        _dateCount = args.value.toString();
        print("Count" + _dateCount);
      } else {
        _rangeCount = args.value.length.toString();
        print("RangeCount" + _rangeCount);
      }
    });
  }
}
