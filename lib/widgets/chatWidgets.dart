// ignore_for_file: prefer_const_constructors

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:silverleaf/contest/color.dart';
import 'package:silverleaf/contest/extension.dart';
import 'package:silverleaf/contest/textstylecontest.dart';
import 'package:silverleaf/main.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:silverleaf/view/dashboard/mainboard.dart';

class MainPageChat extends StatefulWidget {
  final String staff_id;
  final String student_id;
  final String class_name;
  final String Section_name;
  final String Profile;
  final String name;
  final String sender_id;
  final String class_id;
  final String section_id;
  final String branch_id;
  final int message_count;

  const MainPageChat({
    super.key,
    required this.staff_id,
    required this.student_id,
    required this.Section_name,
    required this.class_name,
    required this.Profile,
    required this.name,
    required this.sender_id,
    required this.class_id,
    required this.section_id,
    required this.branch_id,
    required this.message_count,
  });

  @override
  State<MainPageChat> createState() => _MainPageChatState();
}

class _MainPageChatState extends State<MainPageChat> {
  TextEditingController sendMessage = TextEditingController();
  var jsonData;
  late bool _isFetchingData;
  List<Message> message = [];
  List<Message> chat = [];

  List userNames = [];
  List<Message> sortedMessages = [];
  late StreamController<List<Message>> _messageStreamController;
  late ScrollController _scrollController;
  late Timer _timer;
  bool isLoading = true;

  Future<List> list_message() async {
    final url;
    final data;
    var type;

    url = 'https://silverleafms.in/silvar_leaf/api/staffs/get-message';
    data = {
      'staff_id': widget.staff_id,
      'student_id': widget.student_id,
      'sender_id': widget.staff_id,
    };

    final response = await http.post(Uri.parse(url), body: data);

    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);

      var response_student = responseBody['data'];

      List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
        responseBody['data'],
      );

      jsonData.map((data) {
        final msg = Message(
          date:
              data['posted_date'] == null
                  ? DateTime.now()
                  : DateTime.parse(data['posted_date']),
          isSentByMe: data['isSentByMe'],
          text: data['message'],
        );
        setState(() {
          data.length == 0 ? null : chat.add(msg);
        });
      }).toList();

      return response_student;
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<List<Message>> get_message() async {
    try {
      final url = 'https://silverleafms.in/silvar_leaf/api/staffs/get-message';
      final data = {
        'staff_id': widget.staff_id,
        'student_id': widget.student_id,
        'sender_id': widget.student_id,
      };

      final response = await http.post(Uri.parse(url), body: data);

      if (response.statusCode == 200) {
        var responseBody = json.decode(response.body);

        // Handle different response formats
        if (responseBody['data'] is List) {
          List<Map<String, dynamic>> jsonData = List<Map<String, dynamic>>.from(
            responseBody['data'],
          );

          List<Message> messages =
              jsonData.map((data) {
                return Message(
                  date: DateTime.parse(data['posted_date']),
                  isSentByMe: data['isSentByMe'],
                  text: data['message'],
                );
              }).toList();

          // Add messages to the stream controller if not closed
          if (!_messageStreamController.isClosed) {
            _messageStreamController.add(messages);
          }

          return messages;
        } else {
          // Handle case where data is not a list (empty or error response)
          if (!_messageStreamController.isClosed) {
            _messageStreamController.add([]);
          }
          return [];
        }
      } else {
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      // Handle errors gracefully
      if (!_messageStreamController.isClosed) {
        _messageStreamController.addError(e);
      }
      return [];
    }
  }

  save_message() async {
    final url;
    final data;
    var type;
    //final msg = Message(date: DateTime.now(), isSentByMe: int.parse(widget.sender_id), text: sendMessage.text);
    url = 'https://silverleafms.in/silvar_leaf/api/staffs/send-message-teacher';
    data = {
      'message': sendMessage.text,
      'staff_id': widget.staff_id,
      'student_id': widget.student_id,
      'isSentByMe': widget.sender_id,
      'class_id': widget.class_id,
      'section_id': widget.section_id,
      'branch_id': widget.branch_id,
      'posted_date': DateTime.now().toString(),
      'message_status_student': '1',
      'message_status_teacher': '1',
    };

    final response = await http.post(Uri.parse(url), body: data);
    print(response.body);
    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      print(responseBody);
      var response_student = responseBody['data'];
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<void> getListString() async {
    final prefs = await SharedPreferences.getInstance();
    List storedUserNames = prefs.getStringList('users') ?? [];
    if (storedUserNames != null) {
      setState(() {
        userNames = storedUserNames;

        if (userNames.last == '1') {
        } else if (userNames.last == '2') {
          setState(() {
            isTeacher = true;
          });
        } else {}
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _isFetchingData = true;
    _messageStreamController = StreamController<List<Message>>.broadcast();
    getListString();
    _scrollController = ScrollController();
    _timer = Timer.periodic(const Duration(seconds: 5), (_) {
      if (_isFetchingData) {
        get_message();
      }
    });
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
    list_message();
  }

  @override
  void dispose() {
    _isFetchingData = false;
    _timer.cancel();
    _scrollController.dispose();
    if (!_messageStreamController.isClosed) {
      _messageStreamController.close();
    }
    super.dispose();
  }

  String formatDate(DateTime date) {
    DateTime today = DateTime.now();
    DateTime yesterday = today.subtract(Duration(days: 1));

    if (DateFormat.yMMMd().format(date) == DateFormat.yMMMd().format(today)) {
      return 'Today';
    } else if (DateFormat.yMMMd().format(date) ==
        DateFormat.yMMMd().format(yesterday)) {
      return 'Yesterday';
    } else {
      return DateFormat.yMMMd().format(date);
    }
  }

  Future<bool> subback() async {
    globalBottomBarIndex = 3;
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => MainBoard()),
    );
    return Future.value(true);
  }

  void _sendMessage(List<Message> messageList) {
    if (sendMessage.text.trim().isEmpty) return;

    final msg = Message(
      date: DateTime.now(),
      isSentByMe: int.parse(widget.sender_id),
      text: sendMessage.text.trim(),
    );

    setState(() {
      messageList.add(msg);
      save_message();
      sendMessage.clear();
    });

    // Auto-scroll to bottom
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Widget buildMessageItem(message) {
    DateTime dateTime;
    DateFormat inputFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    dateTime = inputFormat.parse(message.date.toString());
    String formattedTime = DateFormat('hh:mm a').format(dateTime);

    bool isMyMessage = message.isSentByMe.toString() == widget.sender_id;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 1.0.hp, horizontal: 4.0.wp),
      child: Row(
        mainAxisAlignment:
            isMyMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Avatar for received messages
          if (!isMyMessage) ...[
            Container(
              margin: EdgeInsets.only(right: 2.0.wp),
              child: CircleAvatar(
                radius: 4.0.wp,
                backgroundColor: AppColors.surfaceVariant,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4.0.wp),
                  child: Image.network(
                    widget.Profile,
                    width: 8.0.wp,
                    height: 8.0.wp,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.person,
                        color: AppColors.textSecondary,
                        size: 4.0.wp,
                      );
                    },
                  ),
                ),
              ),
            ),
          ],

          // Message bubble
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 4.0.wp,
                vertical: 2.0.hp,
              ),
              decoration: BoxDecoration(
                gradient: isMyMessage ? AppColors.primaryGradient : null,
                color: isMyMessage ? null : AppColors.surface,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isMyMessage ? 16 : 4),
                  bottomRight: Radius.circular(isMyMessage ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.cardShadow,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color:
                          isMyMessage
                              ? AppColors.textOnPrimary
                              : AppColors.textPrimary,
                      height: 1.4,
                    ),
                  ),
                  SizedBox(height: 1.0.hp),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        formattedTime,
                        style: AppTextStyles.bodySmall.copyWith(
                          color:
                              isMyMessage
                                  ? AppColors.textOnPrimary.withValues(
                                    alpha: 0.8,
                                  )
                                  : AppColors.textHint,
                          fontSize: 10,
                        ),
                      ),
                      if (isMyMessage) ...[
                        SizedBox(width: 1.0.wp),
                        Icon(
                          Icons.done_all,
                          size: 3.0.wp,
                          color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Avatar for sent messages (optional, usually not shown)
          if (isMyMessage) ...[SizedBox(width: 2.0.wp)],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    String classDisplay = '';
    if (widget.class_name != '') {
      classDisplay = 'Std- ${widget.class_name}, ${widget.Section_name}';
    }

    return SafeArea(
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: PreferredSize(
          preferredSize: Size(double.infinity, 9.0.hp),
          child: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.primaryGradient,
            ),
            child: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: IconButton(
                onPressed: subback,
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: AppColors.textOnPrimary,
                  size: 24,
                ),
              ),
              title: Text(
                'Messages',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              centerTitle: true,
            ),
          ),
        ),
        body: PopScope(
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) {
              subback();
            }
          },
          child: Column(
            children: [
              // Professional Profile Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(4.0.wp),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.cardShadow,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: AppColors.primary, width: 2),
                      ),
                      child: CircleAvatar(
                        radius: 8.0.wp,
                        backgroundColor: AppColors.surfaceVariant,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0.wp),
                          child: Image.network(
                            widget.Profile,
                            width: 16.0.wp,
                            height: 16.0.wp,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: 16.0.wp,
                                height: 16.0.wp,
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(
                                    alpha: 0.1,
                                  ),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.person,
                                  color: AppColors.primary,
                                  size: 8.0.wp,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 4.0.wp),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.name,
                            style: AppTextStyles.headlineSmall.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (classDisplay.isNotEmpty) ...[
                            SizedBox(height: 0.5.hp),
                            Text(
                              classDisplay,
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.all(2.0.wp),
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.circle,
                        color: AppColors.success,
                        size: 3.0.wp,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: StreamBuilder<List<Message>>(
                  stream: _messageStreamController.stream,
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      List<Message> message = snapshot.data!;

                      return Column(
                        children: [
                          Expanded(
                            child: Container(
                              color:
                                  message.isEmpty
                                      ? AppColors.background
                                      : AppColors.surfaceVariant.withValues(
                                        alpha: 0.3,
                                      ),
                              child:
                                  message.isEmpty
                                      ? Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.chat_bubble_outline,
                                              size: 15.0.wp,
                                              color: AppColors.textHint,
                                            ),
                                            SizedBox(height: 2.0.hp),
                                            Text(
                                              'Start a conversation',
                                              style: AppTextStyles.headlineSmall
                                                  .copyWith(
                                                    color:
                                                        AppColors.textSecondary,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                            ),
                                            SizedBox(height: 1.0.hp),
                                            Text(
                                              'Send a message to begin chatting',
                                              style: AppTextStyles.bodyMedium
                                                  .copyWith(
                                                    color: AppColors.textHint,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      )
                                      : ListView.builder(
                                        controller: _scrollController,
                                        padding: EdgeInsets.symmetric(
                                          vertical: 2.0.hp,
                                        ),
                                        itemCount: message.length,
                                        itemBuilder: (context, index) {
                                          final msg = message[index];

                                          // Check if this message's date is different from the previous one
                                          bool showDateHeader =
                                              index == 0 ||
                                              msg.date.day !=
                                                  message[index - 1].date.day ||
                                              msg.date.month !=
                                                  message[index - 1]
                                                      .date
                                                      .month ||
                                              msg.date.year !=
                                                  message[index - 1].date.year;

                                          if (showDateHeader) {
                                            return Column(
                                              children: [
                                                Container(
                                                  margin: EdgeInsets.symmetric(
                                                    vertical: 2.0.hp,
                                                  ),
                                                  child: Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                          horizontal: 4.0.wp,
                                                          vertical: 1.0.hp,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.primary
                                                          .withValues(
                                                            alpha: 0.1,
                                                          ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            20,
                                                          ),
                                                    ),
                                                    child: Text(
                                                      formatDate(msg.date),
                                                      style: AppTextStyles
                                                          .bodySmall
                                                          .copyWith(
                                                            color:
                                                                AppColors
                                                                    .primary,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                buildMessageItem(msg),
                                              ],
                                            );
                                          } else {
                                            return buildMessageItem(msg);
                                          }
                                        },
                                      ),
                            ),
                          ),

                          // Professional Input Field
                          Container(
                            padding: EdgeInsets.all(4.0.wp),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.cardShadow,
                                  blurRadius: 8,
                                  offset: const Offset(0, -2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.surfaceVariant,
                                      borderRadius: BorderRadius.circular(24),
                                      border: Border.all(
                                        color: AppColors.divider,
                                        width: 1,
                                      ),
                                    ),
                                    child: TextField(
                                      controller: sendMessage,
                                      maxLines: null,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: AppTextStyles.bodyMedium,
                                      decoration: InputDecoration(
                                        hintText: "Type your message...",
                                        hintStyle: AppTextStyles.bodyMedium
                                            .copyWith(
                                              color: AppColors.textHint,
                                            ),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 4.0.wp,
                                          vertical: 2.0.hp,
                                        ),
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                      ),
                                      onSubmitted:
                                          (value) => _sendMessage(message),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 2.0.wp),
                                GestureDetector(
                                  onTap: () => _sendMessage(message),
                                  child: Container(
                                    padding: EdgeInsets.all(3.0.wp),
                                    decoration: BoxDecoration(
                                      gradient: AppColors.primaryGradient,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primary.withValues(
                                            alpha: 0.3,
                                          ),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.send,
                                      color: AppColors.textOnPrimary,
                                      size: 5.0.wp,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    } else if (snapshot.hasError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: AppColors.error,
                              size: 15.0.wp,
                            ),
                            SizedBox(height: 2.0.hp),
                            Text(
                              'Failed to load messages',
                              style: AppTextStyles.headlineSmall.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 1.0.hp),
                            Text(
                              'Please check your connection and try again',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      );
                    } else {
                      // Loading state with chat history
                      return Column(
                        children: [
                          Expanded(
                            child: Container(
                              color: AppColors.surfaceVariant.withValues(
                                alpha: 0.3,
                              ),
                              child:
                                  chat.isEmpty
                                      ? Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            CircularProgressIndicator(
                                              color: AppColors.primary,
                                            ),
                                            SizedBox(height: 2.0.hp),
                                            Text(
                                              'Loading messages...',
                                              style: AppTextStyles.bodyMedium
                                                  .copyWith(
                                                    color:
                                                        AppColors.textSecondary,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      )
                                      : ListView.builder(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 2.0.hp,
                                        ),
                                        itemCount: chat.length,
                                        itemBuilder: (context, index) {
                                          final msg = chat[index];

                                          bool showDateHeader =
                                              index == 0 ||
                                              msg.date.day !=
                                                  chat[index - 1].date.day ||
                                              msg.date.month !=
                                                  chat[index - 1].date.month ||
                                              msg.date.year !=
                                                  chat[index - 1].date.year;

                                          if (showDateHeader) {
                                            return Column(
                                              children: [
                                                Container(
                                                  margin: EdgeInsets.symmetric(
                                                    vertical: 2.0.hp,
                                                  ),
                                                  child: Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                          horizontal: 4.0.wp,
                                                          vertical: 1.0.hp,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.primary
                                                          .withValues(
                                                            alpha: 0.1,
                                                          ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            20,
                                                          ),
                                                    ),
                                                    child: Text(
                                                      formatDate(msg.date),
                                                      style: AppTextStyles
                                                          .bodySmall
                                                          .copyWith(
                                                            color:
                                                                AppColors
                                                                    .primary,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                    ),
                                                  ),
                                                ),
                                                buildMessageItem(msg),
                                              ],
                                            );
                                          } else {
                                            return buildMessageItem(msg);
                                          }
                                        },
                                      ),
                            ),
                          ),

                          // Professional Input Field (same as above)
                          Container(
                            padding: EdgeInsets.all(4.0.wp),
                            decoration: BoxDecoration(
                              color: AppColors.surface,
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.cardShadow,
                                  blurRadius: 8,
                                  offset: const Offset(0, -2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.surfaceVariant,
                                      borderRadius: BorderRadius.circular(24),
                                      border: Border.all(
                                        color: AppColors.divider,
                                        width: 1,
                                      ),
                                    ),
                                    child: TextField(
                                      controller: sendMessage,
                                      maxLines: null,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: AppTextStyles.bodyMedium,
                                      decoration: InputDecoration(
                                        hintText: "Type your message...",
                                        hintStyle: AppTextStyles.bodyMedium
                                            .copyWith(
                                              color: AppColors.textHint,
                                            ),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal: 4.0.wp,
                                          vertical: 2.0.hp,
                                        ),
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                      ),
                                      onSubmitted:
                                          (value) => _sendMessage(chat),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 2.0.wp),
                                GestureDetector(
                                  onTap: () => _sendMessage(chat),
                                  child: Container(
                                    padding: EdgeInsets.all(3.0.wp),
                                    decoration: BoxDecoration(
                                      gradient: AppColors.primaryGradient,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppColors.primary.withValues(
                                            alpha: 0.3,
                                          ),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.send,
                                      color: AppColors.textOnPrimary,
                                      size: 5.0.wp,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
                    }
                  },
                  // },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Message {
  final String text;
  final DateTime date;
  final int isSentByMe;
  const Message({
    required this.date,
    required this.isSentByMe,
    required this.text,
  });
}
